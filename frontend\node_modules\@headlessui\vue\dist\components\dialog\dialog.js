import{computed as o,defineComponent as O,h as v,inject as Y,nextTick as se,onMounted as $,onUnmounted as pe,provide as de,ref as y,watchEffect as fe}from"vue";import{FocusTrap as P}from'../../components/focus-trap/focus-trap.js';import{useDocumentOverflowLockedEffect as ge}from'../../hooks/document-overflow/use-document-overflow.js';import{useEventListener as ce}from'../../hooks/use-event-listener.js';import{useId as b}from'../../hooks/use-id.js';import{useInert as _}from'../../hooks/use-inert.js';import{useOutsideClick as ve}from'../../hooks/use-outside-click.js';import{useRootContainers as me}from'../../hooks/use-root-containers.js';import{State as I,useOpenClosed as De}from'../../internal/open-closed.js';import{ForcePortalRoot as F}from'../../internal/portal-force-root.js';import{StackMessage as z,useStackProvider as ye}from'../../internal/stack-context.js';import{Keys as Se}from'../../keyboard.js';import{dom as j}from'../../utils/dom.js';import{match as G}from'../../utils/match.js';import{getOwnerDocument as he}from'../../utils/owner.js';import{Features as V,render as C}from'../../utils/render.js';import{Description as Oe,useDescriptions as Pe}from'../description/description.js';import{Portal as J,PortalGroup as be,useNestedPortals as Ce}from'../portal/portal.js';var Te=(l=>(l[l.Open=0]="Open",l[l.Closed=1]="Closed",l))(Te||{});let H=Symbol("DialogContext");function T(t){let i=Y(H,null);if(i===null){let l=new Error(`<${t} /> is missing a parent <Dialog /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(l,T),l}return i}let A="DC8F892D-2EBD-447C-A4C8-A03058436FF4",Ye=O({name:"Dialog",inheritAttrs:!1,props:{as:{type:[Object,String],default:"div"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},open:{type:[Boolean,String],default:A},initialFocus:{type:Object,default:null},id:{type:String,default:null},role:{type:String,default:"dialog"}},emits:{close:t=>!0},setup(t,{emit:i,attrs:l,slots:p,expose:s}){var q,W;let n=(q=t.id)!=null?q:`headlessui-dialog-${b()}`,u=y(!1);$(()=>{u.value=!0});let r=!1,g=o(()=>t.role==="dialog"||t.role==="alertdialog"?t.role:(r||(r=!0,console.warn(`Invalid role [${g}] passed to <Dialog />. Only \`dialog\` and and \`alertdialog\` are supported. Using \`dialog\` instead.`)),"dialog")),D=y(0),S=De(),R=o(()=>t.open===A&&S!==null?(S.value&I.Open)===I.Open:t.open),m=y(null),E=o(()=>he(m));if(s({el:m,$el:m}),!(t.open!==A||S!==null))throw new Error("You forgot to provide an `open` prop to the `Dialog`.");if(typeof R.value!="boolean")throw new Error(`You provided an \`open\` prop to the \`Dialog\`, but the value is not a boolean. Received: ${R.value===A?void 0:t.open}`);let c=o(()=>u.value&&R.value?0:1),k=o(()=>c.value===0),w=o(()=>D.value>1),N=Y(H,null)!==null,[Q,X]=Ce(),{resolveContainers:B,mainTreeNodeRef:K,MainTreeNode:Z}=me({portals:Q,defaultContainers:[o(()=>{var e;return(e=h.panelRef.value)!=null?e:m.value})]}),ee=o(()=>w.value?"parent":"leaf"),U=o(()=>S!==null?(S.value&I.Closing)===I.Closing:!1),te=o(()=>N||U.value?!1:k.value),le=o(()=>{var e,a,d;return(d=Array.from((a=(e=E.value)==null?void 0:e.querySelectorAll("body > *"))!=null?a:[]).find(f=>f.id==="headlessui-portal-root"?!1:f.contains(j(K))&&f instanceof HTMLElement))!=null?d:null});_(le,te);let ae=o(()=>w.value?!0:k.value),oe=o(()=>{var e,a,d;return(d=Array.from((a=(e=E.value)==null?void 0:e.querySelectorAll("[data-headlessui-portal]"))!=null?a:[]).find(f=>f.contains(j(K))&&f instanceof HTMLElement))!=null?d:null});_(oe,ae),ye({type:"Dialog",enabled:o(()=>c.value===0),element:m,onUpdate:(e,a)=>{if(a==="Dialog")return G(e,{[z.Add]:()=>D.value+=1,[z.Remove]:()=>D.value-=1})}});let re=Pe({name:"DialogDescription",slot:o(()=>({open:R.value}))}),M=y(null),h={titleId:M,panelRef:y(null),dialogState:c,setTitleId(e){M.value!==e&&(M.value=e)},close(){i("close",!1)}};de(H,h);let ne=o(()=>!(!k.value||w.value));ve(B,(e,a)=>{e.preventDefault(),h.close(),se(()=>a==null?void 0:a.focus())},ne);let ie=o(()=>!(w.value||c.value!==0));ce((W=E.value)==null?void 0:W.defaultView,"keydown",e=>{ie.value&&(e.defaultPrevented||e.key===Se.Escape&&(e.preventDefault(),e.stopPropagation(),h.close()))});let ue=o(()=>!(U.value||c.value!==0||N));return ge(E,ue,e=>{var a;return{containers:[...(a=e.containers)!=null?a:[],B]}}),fe(e=>{if(c.value!==0)return;let a=j(m);if(!a)return;let d=new ResizeObserver(f=>{for(let L of f){let x=L.target.getBoundingClientRect();x.x===0&&x.y===0&&x.width===0&&x.height===0&&h.close()}});d.observe(a),e(()=>d.disconnect())}),()=>{let{open:e,initialFocus:a,...d}=t,f={...l,ref:m,id:n,role:g.value,"aria-modal":c.value===0?!0:void 0,"aria-labelledby":M.value,"aria-describedby":re.value},L={open:c.value===0};return v(F,{force:!0},()=>[v(J,()=>v(be,{target:m.value},()=>v(F,{force:!1},()=>v(P,{initialFocus:a,containers:B,features:k.value?G(ee.value,{parent:P.features.RestoreFocus,leaf:P.features.All&~P.features.FocusLock}):P.features.None},()=>v(X,{},()=>C({ourProps:f,theirProps:{...d,...l},slot:L,attrs:l,slots:p,visible:c.value===0,features:V.RenderStrategy|V.Static,name:"Dialog"})))))),v(Z)])}}}),_e=O({name:"DialogOverlay",props:{as:{type:[Object,String],default:"div"},id:{type:String,default:null}},setup(t,{attrs:i,slots:l}){var u;let p=(u=t.id)!=null?u:`headlessui-dialog-overlay-${b()}`,s=T("DialogOverlay");function n(r){r.target===r.currentTarget&&(r.preventDefault(),r.stopPropagation(),s.close())}return()=>{let{...r}=t;return C({ourProps:{id:p,"aria-hidden":!0,onClick:n},theirProps:r,slot:{open:s.dialogState.value===0},attrs:i,slots:l,name:"DialogOverlay"})}}}),ze=O({name:"DialogBackdrop",props:{as:{type:[Object,String],default:"div"},id:{type:String,default:null}},inheritAttrs:!1,setup(t,{attrs:i,slots:l,expose:p}){var r;let s=(r=t.id)!=null?r:`headlessui-dialog-backdrop-${b()}`,n=T("DialogBackdrop"),u=y(null);return p({el:u,$el:u}),$(()=>{if(n.panelRef.value===null)throw new Error("A <DialogBackdrop /> component is being used, but a <DialogPanel /> component is missing.")}),()=>{let{...g}=t,D={id:s,ref:u,"aria-hidden":!0};return v(F,{force:!0},()=>v(J,()=>C({ourProps:D,theirProps:{...i,...g},slot:{open:n.dialogState.value===0},attrs:i,slots:l,name:"DialogBackdrop"})))}}}),Ge=O({name:"DialogPanel",props:{as:{type:[Object,String],default:"div"},id:{type:String,default:null}},setup(t,{attrs:i,slots:l,expose:p}){var r;let s=(r=t.id)!=null?r:`headlessui-dialog-panel-${b()}`,n=T("DialogPanel");p({el:n.panelRef,$el:n.panelRef});function u(g){g.stopPropagation()}return()=>{let{...g}=t,D={id:s,ref:n.panelRef,onClick:u};return C({ourProps:D,theirProps:g,slot:{open:n.dialogState.value===0},attrs:i,slots:l,name:"DialogPanel"})}}}),Ve=O({name:"DialogTitle",props:{as:{type:[Object,String],default:"h2"},id:{type:String,default:null}},setup(t,{attrs:i,slots:l}){var n;let p=(n=t.id)!=null?n:`headlessui-dialog-title-${b()}`,s=T("DialogTitle");return $(()=>{s.setTitleId(p),pe(()=>s.setTitleId(null))}),()=>{let{...u}=t;return C({ourProps:{id:p},theirProps:u,slot:{open:s.dialogState.value===0},attrs:i,slots:l,name:"DialogTitle"})}}}),Je=Oe;export{Ye as Dialog,ze as DialogBackdrop,Je as DialogDescription,_e as DialogOverlay,Ge as DialogPanel,Ve as DialogTitle};
