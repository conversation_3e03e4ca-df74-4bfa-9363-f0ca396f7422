{"info": {"_postman_id": "jwt-demo-api-collection", "name": "JWT Demo API - Inventory Management", "description": "Complete API collection for the JWT Demo Inventory Management System", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Authentication", "item": [{"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"testuser\",\n  \"password\": \"password\"\n}"}, "url": {"raw": "{{base_url}}/login", "host": ["{{base_url}}"], "path": ["login"]}, "description": "Authenticate user and get JWT token"}, "response": [], "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.environment.set('jwt_token', response.token);", "    console.log('JWT <PERSON> saved:', response.token);", "}"], "type": "text/javascript"}}]}, {"name": "Logout", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/logout", "host": ["{{base_url}}"], "path": ["logout"]}, "description": "Logout user and invalidate JWT token"}, "response": [], "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    pm.environment.unset('jwt_token');", "    console.log('JWT Token removed from environment');", "}"], "type": "text/javascript"}}]}], "description": "Authentication endpoints for login and logout"}, {"name": "Inventory Management", "item": [{"name": "Get All Inventory Items", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/inventory", "host": ["{{base_url}}"], "path": ["inventory"]}, "description": "Retrieve all inventory items (requires authentication)"}, "response": []}, {"name": "Get Inventory Item by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/inventory/1", "host": ["{{base_url}}"], "path": ["inventory", "1"]}, "description": "Retrieve a specific inventory item by ID"}, "response": []}, {"name": "Create New Inventory Item", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Laptop Dell XPS 13\",\n  \"description\": \"13-inch premium laptop with Intel i7 processor\",\n  \"quantity\": 15,\n  \"price\": 1299.99,\n  \"category\": \"Electronics\",\n  \"sku\": \"LAP-DELL-XPS13\"\n}"}, "url": {"raw": "{{base_url}}/inventory", "host": ["{{base_url}}"], "path": ["inventory"]}, "description": "Create a new inventory item"}, "response": []}, {"name": "Update Inventory Item", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Laptop Dell XPS 13\",\n  \"description\": \"Updated description for the laptop\",\n  \"quantity\": 20,\n  \"price\": 1399.99,\n  \"category\": \"Electronics\",\n  \"sku\": \"LAP-DELL-XPS13-UPDATED\"\n}"}, "url": {"raw": "{{base_url}}/inventory/1", "host": ["{{base_url}}"], "path": ["inventory", "1"]}, "description": "Update an existing inventory item"}, "response": []}, {"name": "Delete Inventory Item", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/inventory/1", "host": ["{{base_url}}"], "path": ["inventory", "1"]}, "description": "Delete an inventory item by ID"}, "response": []}, {"name": "Search Inventory Items", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/inventory/search/laptop", "host": ["{{base_url}}"], "path": ["inventory", "search", "laptop"]}, "description": "Search inventory items by name, description, category, or SKU"}, "response": []}], "description": "Inventory management endpoints (all require authentication)"}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "base_url", "value": "http://localhost:8000", "type": "string"}]}