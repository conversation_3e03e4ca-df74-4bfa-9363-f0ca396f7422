import{defineComponent as l,inject as a,provide as c}from"vue";import{render as p}from'../utils/render.js';let e=Symbol("ForcePortalRootContext");function s(){return a(e,!1)}let u=l({name:"ForcePortalRoot",props:{as:{type:[Object,String],default:"template"},force:{type:Boolean,default:!1}},setup(o,{slots:t,attrs:r}){return c(e,o.force),()=>{let{force:f,...n}=o;return p({theirProps:n,ourProps:{},slot:{},slots:t,attrs:r,name:"ForcePortalRoot"})}}});export{u as ForcePortalRoot,s as usePortalRoot};
