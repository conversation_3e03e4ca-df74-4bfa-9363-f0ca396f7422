import{defineComponent as a}from"vue";import{render as p}from'../utils/render.js';var u=(e=>(e[e.None=1]="None",e[e.Focusable=2]="Focusable",e[e.Hidden=4]="Hidden",e))(u||{});let f=a({name:"Hidden",props:{as:{type:[Object,String],default:"div"},features:{type:Number,default:1}},setup(t,{slots:n,attrs:i}){return()=>{var r;let{features:e,...d}=t,o={"aria-hidden":(e&2)===2?!0:(r=d["aria-hidden"])!=null?r:void 0,hidden:(e&4)===4?!0:void 0,style:{position:"fixed",top:1,left:1,width:1,height:0,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0",...(e&4)===4&&(e&2)!==2&&{display:"none"}}};return p({ourProps:o,theirProps:d,slot:{},attrs:i,slots:n,name:"Hidden"})}}});export{u as Features,f as Hidden};
