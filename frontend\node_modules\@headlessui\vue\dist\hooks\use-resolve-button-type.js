import{onMounted as i,ref as f,watchEffect as l}from"vue";import{dom as o}from'../utils/dom.js';function r(t,e){if(t)return t;let n=e!=null?e:"button";if(typeof n=="string"&&n.toLowerCase()==="button")return"button"}function s(t,e){let n=f(r(t.value.type,t.value.as));return i(()=>{n.value=r(t.value.type,t.value.as)}),l(()=>{var u;n.value||o(e)&&o(e)instanceof HTMLButtonElement&&!((u=o(e))!=null&&u.hasAttribute("type"))&&(n.value="button")}),n}export{s as useResolveButtonType};
