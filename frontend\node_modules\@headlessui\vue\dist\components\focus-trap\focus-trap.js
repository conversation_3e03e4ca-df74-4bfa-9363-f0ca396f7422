import{computed as L,defineComponent as I,Fragment as j,h as R,onMounted as M,onUnmounted as h,ref as E,watch as g,watchEffect as K}from"vue";import{useEventListener as U}from'../../hooks/use-event-listener.js';import{Direction as y,useTabDirection as _}from'../../hooks/use-tab-direction.js';import{Features as k,Hidden as D}from'../../internal/hidden.js';import{history as C}from'../../utils/active-element-history.js';import{dom as c}from'../../utils/dom.js';import{Focus as v,focusElement as p,focusIn as b,FocusResult as q}from'../../utils/focus-management.js';import{match as P}from'../../utils/match.js';import{microTask as S}from'../../utils/micro-task.js';import{getOwnerDocument as x}from'../../utils/owner.js';import{render as G}from'../../utils/render.js';function B(t){if(!t)return new Set;if(typeof t=="function")return new Set(t());let n=new Set;for(let r of t.value){let l=c(r);l instanceof HTMLElement&&n.add(l)}return n}var A=(e=>(e[e.None=1]="None",e[e.InitialFocus=2]="InitialFocus",e[e.TabLock=4]="TabLock",e[e.FocusLock=8]="FocusLock",e[e.RestoreFocus=16]="RestoreFocus",e[e.All=30]="All",e))(A||{});let ue=Object.assign(I({name:"FocusTrap",props:{as:{type:[Object,String],default:"div"},initialFocus:{type:Object,default:null},features:{type:Number,default:30},containers:{type:[Object,Function],default:E(new Set)}},inheritAttrs:!1,setup(t,{attrs:n,slots:r,expose:l}){let o=E(null);l({el:o,$el:o});let i=L(()=>x(o)),e=E(!1);M(()=>e.value=!0),h(()=>e.value=!1),$({ownerDocument:i},L(()=>e.value&&Boolean(t.features&16)));let m=z({ownerDocument:i,container:o,initialFocus:L(()=>t.initialFocus)},L(()=>e.value&&Boolean(t.features&2)));J({ownerDocument:i,container:o,containers:t.containers,previousActiveElement:m},L(()=>e.value&&Boolean(t.features&8)));let f=_();function a(u){let T=c(o);if(!T)return;(w=>w())(()=>{P(f.value,{[y.Forwards]:()=>{b(T,v.First,{skipElements:[u.relatedTarget]})},[y.Backwards]:()=>{b(T,v.Last,{skipElements:[u.relatedTarget]})}})})}let s=E(!1);function F(u){u.key==="Tab"&&(s.value=!0,requestAnimationFrame(()=>{s.value=!1}))}function H(u){if(!e.value)return;let T=B(t.containers);c(o)instanceof HTMLElement&&T.add(c(o));let d=u.relatedTarget;d instanceof HTMLElement&&d.dataset.headlessuiFocusGuard!=="true"&&(N(T,d)||(s.value?b(c(o),P(f.value,{[y.Forwards]:()=>v.Next,[y.Backwards]:()=>v.Previous})|v.WrapAround,{relativeTo:u.target}):u.target instanceof HTMLElement&&p(u.target)))}return()=>{let u={},T={ref:o,onKeydown:F,onFocusout:H},{features:d,initialFocus:w,containers:Q,...O}=t;return R(j,[Boolean(d&4)&&R(D,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:a,features:k.Focusable}),G({ourProps:T,theirProps:{...n,...O},slot:u,attrs:n,slots:r,name:"FocusTrap"}),Boolean(d&4)&&R(D,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:a,features:k.Focusable})])}}}),{features:A});function W(t){let n=E(C.slice());return g([t],([r],[l])=>{l===!0&&r===!1?S(()=>{n.value.splice(0)}):l===!1&&r===!0&&(n.value=C.slice())},{flush:"post"}),()=>{var r;return(r=n.value.find(l=>l!=null&&l.isConnected))!=null?r:null}}function $({ownerDocument:t},n){let r=W(n);M(()=>{K(()=>{var l,o;n.value||((l=t.value)==null?void 0:l.activeElement)===((o=t.value)==null?void 0:o.body)&&p(r())},{flush:"post"})}),h(()=>{n.value&&p(r())})}function z({ownerDocument:t,container:n,initialFocus:r},l){let o=E(null),i=E(!1);return M(()=>i.value=!0),h(()=>i.value=!1),M(()=>{g([n,r,l],(e,m)=>{if(e.every((a,s)=>(m==null?void 0:m[s])===a)||!l.value)return;let f=c(n);f&&S(()=>{var F,H;if(!i.value)return;let a=c(r),s=(F=t.value)==null?void 0:F.activeElement;if(a){if(a===s){o.value=s;return}}else if(f.contains(s)){o.value=s;return}a?p(a):b(f,v.First|v.NoScroll)===q.Error&&console.warn("There are no focusable elements inside the <FocusTrap />"),o.value=(H=t.value)==null?void 0:H.activeElement})},{immediate:!0,flush:"post"})}),o}function J({ownerDocument:t,container:n,containers:r,previousActiveElement:l},o){var i;U((i=t.value)==null?void 0:i.defaultView,"focus",e=>{if(!o.value)return;let m=B(r);c(n)instanceof HTMLElement&&m.add(c(n));let f=l.value;if(!f)return;let a=e.target;a&&a instanceof HTMLElement?N(m,a)?(l.value=a,p(a)):(e.preventDefault(),e.stopPropagation(),p(f)):p(l.value)},!0)}function N(t,n){for(let r of t)if(r.contains(n))return!0;return!1}export{ue as FocusTrap};
