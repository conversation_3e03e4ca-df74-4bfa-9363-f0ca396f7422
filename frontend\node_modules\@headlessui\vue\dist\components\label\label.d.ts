import { type ComputedRef } from 'vue';
export declare function useLabels({ slot, name, props, }?: {
    slot?: Record<string, unknown>;
    name?: string;
    props?: Record<string, unknown>;
}): ComputedRef<string | undefined>;
export declare let Label: import("vue").DefineComponent<{
    as: {
        type: (ObjectConstructor | StringConstructor)[];
        default: string;
    };
    passive: {
        type: BooleanConstructor[];
        default: boolean;
    };
    id: {
        type: StringConstructor;
        default: null;
    };
}, () => import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
    [key: string]: any;
}> | import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
    [key: string]: any;
}>[] | null, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    as: {
        type: (ObjectConstructor | StringConstructor)[];
        default: string;
    };
    passive: {
        type: BooleanConstructor[];
        default: boolean;
    };
    id: {
        type: StringConstructor;
        default: null;
    };
}>>, {
    passive: boolean;
    id: string;
    as: string | Record<string, any>;
}>;
