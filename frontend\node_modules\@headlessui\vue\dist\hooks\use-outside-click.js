import{computed as s,ref as E}from"vue";import{dom as p}from'../utils/dom.js';import{FocusableMode as d,isFocusableElement as C}from'../utils/focus-management.js';import{isMobile as T}from'../utils/platform.js';import{useDocumentEvent as i}from'./use-document-event.js';import{useWindowEvent as M}from'./use-window-event.js';function w(f,m,l=s(()=>!0)){function a(e,r){if(!l.value||e.defaultPrevented)return;let t=r(e);if(t===null||!t.getRootNode().contains(t))return;let c=function o(n){return typeof n=="function"?o(n()):Array.isArray(n)||n instanceof Set?n:[n]}(f);for(let o of c){if(o===null)continue;let n=o instanceof HTMLElement?o:p(o);if(n!=null&&n.contains(t)||e.composed&&e.composedPath().includes(n))return}return!C(t,d.Loose)&&t.tabIndex!==-1&&e.preventDefault(),m(e,t)}let u=E(null);i("pointerdown",e=>{var r,t;l.value&&(u.value=((t=(r=e.composedPath)==null?void 0:r.call(e))==null?void 0:t[0])||e.target)},!0),i("mousedown",e=>{var r,t;l.value&&(u.value=((t=(r=e.composedPath)==null?void 0:r.call(e))==null?void 0:t[0])||e.target)},!0),i("click",e=>{T()||u.value&&(a(e,()=>u.value),u.value=null)},!0),i("touchend",e=>a(e,()=>e.target instanceof HTMLElement?e.target:null),!0),M("blur",e=>a(e,()=>window.document.activeElement instanceof HTMLIFrameElement?window.document.activeElement:null),!0)}export{w as useOutsideClick};
