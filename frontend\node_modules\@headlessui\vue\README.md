<h3 align="center">
  @headlessui/vue
</h3>

<p align="center">
  A set of completely unstyled, fully accessible UI components for Vue 3, designed to integrate
  beautifully with Tailwind CSS.
</p>

<p align="center">
  <a href="https://www.npmjs.com/package/@headlessui/vue"><img src="https://img.shields.io/npm/dt/@headlessui/vue.svg" alt="Total Downloads"></a>
  <a href="https://github.com/tailwindlabs/headlessui/releases"><img src="https://img.shields.io/npm/v/@headlessui/vue.svg" alt="Latest Release"></a>
  <a href="https://github.com/tailwindlabs/headlessui/blob/main/LICENSE"><img src="https://img.shields.io/npm/l/@headlessui/vue.svg" alt="License"></a>
</p>

## Installation

Please note that **this library only supports Vue 3**.

```sh
npm install @headlessui/vue
```

## Documentation

For full documentation, visit [headlessui.dev](https://headlessui.dev/vue/menu).

## Community

For help, discussion about best practices, or any other conversation that would benefit from being searchable:

[Discuss Headless UI on GitHub](https://github.com/tailwindlabs/headlessui/discussions)

For casual chit-chat with others using the library:

[Join the Tailwind CSS Discord Server](https://discord.gg/7NF8GNe)
