"use strict";var yo=Object.create;var Ue=Object.defineProperty;var So=Object.getOwnPropertyDescriptor;var xo=Object.getOwnPropertyNames;var To=Object.getPrototypeOf,Eo=Object.prototype.hasOwnProperty;var Ro=(e,o,n)=>o in e?Ue(e,o,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[o]=n;var Oo=(e,o)=>{for(var n in o)Ue(e,n,{get:o[n],enumerable:!0})},qt=(e,o,n,a)=>{if(o&&typeof o=="object"||typeof o=="function")for(let r of xo(o))!Eo.call(e,r)&&r!==n&&Ue(e,r,{get:()=>o[r],enumerable:!(a=So(o,r))||a.enumerable});return e};var wo=(e,o,n)=>(n=e!=null?yo(To(e)):{},qt(o||!e||!e.__esModule?Ue(n,"default",{value:e,enumerable:!0}):n,e)),Co=e=>qt(Ue({},"__esModule",{value:!0}),e);var It=(e,o,n)=>(Ro(e,typeof o!="symbol"?o+"":o,n),n);var qr={};Oo(qr,{Combobox:()=>Ko,ComboboxButton:()=>_o,ComboboxInput:()=>Uo,ComboboxLabel:()=>$o,ComboboxOption:()=>zo,ComboboxOptions:()=>Wo,Dialog:()=>nr,DialogBackdrop:()=>rr,DialogDescription:()=>ir,DialogOverlay:()=>or,DialogPanel:()=>lr,DialogTitle:()=>ar,Disclosure:()=>sr,DisclosureButton:()=>dr,DisclosurePanel:()=>fr,FocusTrap:()=>Ie,Listbox:()=>mr,ListboxButton:()=>br,ListboxLabel:()=>vr,ListboxOption:()=>hr,ListboxOptions:()=>gr,Menu:()=>Sr,MenuButton:()=>xr,MenuItem:()=>Er,MenuItems:()=>Tr,Popover:()=>lo,PopoverButton:()=>Or,PopoverGroup:()=>Pr,PopoverOverlay:()=>wr,PopoverPanel:()=>Cr,Portal:()=>Je,PortalGroup:()=>ht,RadioGroup:()=>Mr,RadioGroupDescription:()=>Fr,RadioGroupLabel:()=>Lr,RadioGroupOption:()=>Dr,Switch:()=>Hr,SwitchDescription:()=>jr,SwitchGroup:()=>kr,SwitchLabel:()=>Ar,Tab:()=>Vr,TabGroup:()=>Nr,TabList:()=>Br,TabPanel:()=>$r,TabPanels:()=>Kr,TransitionChild:()=>go,TransitionRoot:()=>ho,provideUseId:()=>un});module.exports=Co(qr);function We(){return We=Object.assign?Object.assign.bind():function(e){for(var o=1;o<arguments.length;o++){var n=arguments[o];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},We.apply(this,arguments)}function ze(){return ze=Object.assign?Object.assign.bind():function(e){for(var o=1;o<arguments.length;o++){var n=arguments[o];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},ze.apply(this,arguments)}function Te(e,o,n){var a,r=(a=n.initialDeps)!=null?a:[],t;return function(){var l;n.key&&n.debug!=null&&n.debug()&&(l=Date.now());var i=e(),u=i.length!==r.length||i.some(function(y,b){return r[b]!==y});if(!u)return t;r=i;var p;if(n.key&&n.debug!=null&&n.debug()&&(p=Date.now()),t=o.apply(void 0,i),n.key&&n.debug!=null&&n.debug()){var d=Math.round((Date.now()-l)*100)/100,f=Math.round((Date.now()-p)*100)/100,s=f/16,S=function(b,g){for(b=String(b);b.length<g;)b=" "+b;return b};console.info("%c\u23F1 "+S(f,5)+" /"+S(d,5)+" ms",`
            font-size: .6rem;
            font-weight: bold;
            color: hsl(`+Math.max(0,Math.min(120-120*s,120))+"deg 100% 31%);",n==null?void 0:n.key)}return n==null||n.onChange==null||n.onChange(t),t}}function rt(e,o){if(e===void 0)throw new Error("Unexpected undefined"+(o?": "+o:""));return e}var Yt=function(o,n){return Math.abs(o-n)<1};var Po=function(o){return o},Io=function(o){for(var n=Math.max(o.startIndex-o.overscan,0),a=Math.min(o.endIndex+o.overscan,o.count-1),r=[],t=n;t<=a;t++)r.push(t);return r},Qt=function(o,n){var a=o.scrollElement;if(a){var r=function(i){var u=i.width,p=i.height;n({width:Math.round(u),height:Math.round(p)})};r(a.getBoundingClientRect());var t=new ResizeObserver(function(l){var i=l[0];if(i!=null&&i.borderBoxSize){var u=i.borderBoxSize[0];if(u){r({width:u.inlineSize,height:u.blockSize});return}}r(a.getBoundingClientRect())});return t.observe(a,{box:"border-box"}),function(){t.unobserve(a)}}};var Jt=function(o,n){var a=o.scrollElement;if(a){var r=function(){n(a[o.options.horizontal?"scrollLeft":"scrollTop"])};return r(),a.addEventListener("scroll",r,{passive:!0}),function(){a.removeEventListener("scroll",r)}}};var Mo=function(o,n,a){if(n!=null&&n.borderBoxSize){var r=n.borderBoxSize[0];if(r){var t=Math.round(r[a.options.horizontal?"inlineSize":"blockSize"]);return t}}return Math.round(o.getBoundingClientRect()[a.options.horizontal?"width":"height"])};var Xt=function(o,n,a){var r,t,l=n.adjustments,i=l===void 0?0:l,u=n.behavior,p=o+i;(r=a.scrollElement)==null||r.scrollTo==null||r.scrollTo((t={},t[a.options.horizontal?"left":"top"]=p,t.behavior=u,t))},Zt=function(o){var n=this;this.unsubs=[],this.scrollElement=null,this.isScrolling=!1,this.isScrollingTimeoutId=null,this.scrollToIndexTimeoutId=null,this.measurementsCache=[],this.itemSizeCache=new Map,this.pendingMeasuredCacheIndexes=[],this.scrollDirection=null,this.scrollAdjustments=0,this.measureElementCache=new Map,this.observer=function(){var a=null,r=function(){return a||(typeof ResizeObserver!="undefined"?a=new ResizeObserver(function(l){l.forEach(function(i){n._measureElement(i.target,i)})}):null)};return{disconnect:function(){var l;return(l=r())==null?void 0:l.disconnect()},observe:function(l){var i;return(i=r())==null?void 0:i.observe(l,{box:"border-box"})},unobserve:function(l){var i;return(i=r())==null?void 0:i.unobserve(l)}}}(),this.range={startIndex:0,endIndex:0},this.setOptions=function(a){Object.entries(a).forEach(function(r){var t=r[0],l=r[1];typeof l=="undefined"&&delete a[t]}),n.options=ze({debug:!1,initialOffset:0,overscan:1,paddingStart:0,paddingEnd:0,scrollPaddingStart:0,scrollPaddingEnd:0,horizontal:!1,getItemKey:Po,rangeExtractor:Io,onChange:function(){},measureElement:Mo,initialRect:{width:0,height:0},scrollMargin:0,scrollingDelay:150,indexAttribute:"data-index",initialMeasurementsCache:[],lanes:1},a)},this.notify=function(){n.options.onChange==null||n.options.onChange(n)},this.cleanup=function(){n.unsubs.filter(Boolean).forEach(function(a){return a()}),n.unsubs=[],n.scrollElement=null},this._didMount=function(){return n.measureElementCache.forEach(n.observer.observe),function(){n.observer.disconnect(),n.cleanup()}},this._willUpdate=function(){var a=n.options.getScrollElement();n.scrollElement!==a&&(n.cleanup(),n.scrollElement=a,n._scrollToOffset(n.scrollOffset,{adjustments:void 0,behavior:void 0}),n.unsubs.push(n.options.observeElementRect(n,function(r){var t=n.scrollRect;n.scrollRect=r,(n.options.horizontal?r.width!==t.width:r.height!==t.height)&&n.maybeNotify()})),n.unsubs.push(n.options.observeElementOffset(n,function(r){n.scrollAdjustments=0,n.scrollOffset!==r&&(n.isScrollingTimeoutId!==null&&(clearTimeout(n.isScrollingTimeoutId),n.isScrollingTimeoutId=null),n.isScrolling=!0,n.scrollDirection=n.scrollOffset<r?"forward":"backward",n.scrollOffset=r,n.maybeNotify(),n.isScrollingTimeoutId=setTimeout(function(){n.isScrollingTimeoutId=null,n.isScrolling=!1,n.scrollDirection=null,n.maybeNotify()},n.options.scrollingDelay))})))},this.getSize=function(){return n.scrollRect[n.options.horizontal?"width":"height"]},this.memoOptions=Te(function(){return[n.options.count,n.options.paddingStart,n.options.scrollMargin,n.options.getItemKey]},function(a,r,t,l){return n.pendingMeasuredCacheIndexes=[],{count:a,paddingStart:r,scrollMargin:t,getItemKey:l}},{key:!1}),this.getFurthestMeasurement=function(a,r){for(var t=new Map,l=new Map,i=r-1;i>=0;i--){var u=a[i];if(!t.has(u.lane)){var p=l.get(u.lane);if(p==null||u.end>p.end?l.set(u.lane,u):u.end<p.end&&t.set(u.lane,!0),t.size===n.options.lanes)break}}return l.size===n.options.lanes?Array.from(l.values()).sort(function(d,f){return d.end-f.end})[0]:void 0},this.getMeasurements=Te(function(){return[n.memoOptions(),n.itemSizeCache]},function(a,r){var t=a.count,l=a.paddingStart,i=a.scrollMargin,u=a.getItemKey,p=n.pendingMeasuredCacheIndexes.length>0?Math.min.apply(Math,n.pendingMeasuredCacheIndexes):0;n.pendingMeasuredCacheIndexes=[];for(var d=n.measurementsCache.slice(0,p),f=p;f<t;f++){var s=u(f),S=n.options.lanes===1?d[f-1]:n.getFurthestMeasurement(d,f),y=S?S.end:l+i,b=r.get(s),g=typeof b=="number"?b:n.options.estimateSize(f),T=y+g,m=S?S.lane:f%n.options.lanes;d[f]={index:f,start:y,size:g,end:T,key:s,lane:m}}return n.measurementsCache=d,d},{key:!1,debug:function(){return n.options.debug}}),this.calculateRange=Te(function(){return[n.getMeasurements(),n.getSize(),n.scrollOffset]},function(a,r,t){return n.range=Do({measurements:a,outerSize:r,scrollOffset:t})},{key:!1,debug:function(){return n.options.debug}}),this.maybeNotify=Te(function(){var a=n.calculateRange();return[a.startIndex,a.endIndex,n.isScrolling]},function(){n.notify()},{key:!1,debug:function(){return n.options.debug},initialDeps:[this.range.startIndex,this.range.endIndex,this.isScrolling]}),this.getIndexes=Te(function(){return[n.options.rangeExtractor,n.calculateRange(),n.options.overscan,n.options.count]},function(a,r,t,l){return a(ze({},r,{overscan:t,count:l}))},{key:!1,debug:function(){return n.options.debug}}),this.indexFromElement=function(a){var r=n.options.indexAttribute,t=a.getAttribute(r);return t?parseInt(t,10):(console.warn("Missing attribute name '"+r+"={index}' on measured element."),-1)},this._measureElement=function(a,r){var t=n.measurementsCache[n.indexFromElement(a)];if(!t){n.measureElementCache.forEach(function(u,p){u===a&&(n.observer.unobserve(a),n.measureElementCache.delete(p))});return}var l=n.measureElementCache.get(t.key);if(!a.isConnected){l&&(n.observer.unobserve(l),n.measureElementCache.delete(t.key));return}l!==a&&(l&&n.observer.unobserve(l),n.observer.observe(a),n.measureElementCache.set(t.key,a));var i=n.options.measureElement(a,r,n);n.resizeItem(t,i)},this.resizeItem=function(a,r){var t,l=(t=n.itemSizeCache.get(a.key))!=null?t:a.size,i=r-l;i!==0&&(a.start<n.scrollOffset&&n._scrollToOffset(n.scrollOffset,{adjustments:n.scrollAdjustments+=i,behavior:void 0}),n.pendingMeasuredCacheIndexes.push(a.index),n.itemSizeCache=new Map(n.itemSizeCache.set(a.key,r)),n.notify())},this.measureElement=function(a){a&&n._measureElement(a,void 0)},this.getVirtualItems=Te(function(){return[n.getIndexes(),n.getMeasurements()]},function(a,r){for(var t=[],l=0,i=a.length;l<i;l++){var u=a[l],p=r[u];t.push(p)}return t},{key:!1,debug:function(){return n.options.debug}}),this.getVirtualItemForOffset=function(a){var r=n.getMeasurements();return rt(r[en(0,r.length-1,function(t){return rt(r[t]).start},a)])},this.getOffsetForAlignment=function(a,r){var t=n.getSize();r==="auto"&&(a<=n.scrollOffset?r="start":a>=n.scrollOffset+t?r="end":r="start"),r==="start"?a=a:r==="end"?a=a-t:r==="center"&&(a=a-t/2);var l=n.options.horizontal?"scrollWidth":"scrollHeight",i=n.scrollElement?"document"in n.scrollElement?n.scrollElement.document.documentElement[l]:n.scrollElement[l]:0,u=i-n.getSize();return Math.max(Math.min(u,a),0)},this.getOffsetForIndex=function(a,r){r===void 0&&(r="auto"),a=Math.max(0,Math.min(a,n.options.count-1));var t=rt(n.getMeasurements()[a]);if(r==="auto")if(t.end>=n.scrollOffset+n.getSize()-n.options.scrollPaddingEnd)r="end";else if(t.start<=n.scrollOffset+n.options.scrollPaddingStart)r="start";else return[n.scrollOffset,r];var l=r==="end"?t.end+n.options.scrollPaddingEnd:t.start-n.options.scrollPaddingStart;return[n.getOffsetForAlignment(l,r),r]},this.isDynamicMode=function(){return n.measureElementCache.size>0},this.cancelScrollToIndex=function(){n.scrollToIndexTimeoutId!==null&&(clearTimeout(n.scrollToIndexTimeoutId),n.scrollToIndexTimeoutId=null)},this.scrollToOffset=function(a,r){var t=r===void 0?{}:r,l=t.align,i=l===void 0?"start":l,u=t.behavior;n.cancelScrollToIndex(),u==="smooth"&&n.isDynamicMode()&&console.warn("The `smooth` scroll behavior is not fully supported with dynamic size."),n._scrollToOffset(n.getOffsetForAlignment(a,i),{adjustments:void 0,behavior:u})},this.scrollToIndex=function(a,r){var t=r===void 0?{}:r,l=t.align,i=l===void 0?"auto":l,u=t.behavior;a=Math.max(0,Math.min(a,n.options.count-1)),n.cancelScrollToIndex(),u==="smooth"&&n.isDynamicMode()&&console.warn("The `smooth` scroll behavior is not fully supported with dynamic size.");var p=n.getOffsetForIndex(a,i),d=p[0],f=p[1];n._scrollToOffset(d,{adjustments:void 0,behavior:u}),u!=="smooth"&&n.isDynamicMode()&&(n.scrollToIndexTimeoutId=setTimeout(function(){n.scrollToIndexTimeoutId=null;var s=n.measureElementCache.has(n.options.getItemKey(a));if(s){var S=n.getOffsetForIndex(a,f),y=S[0];Yt(y,n.scrollOffset)||n.scrollToIndex(a,{align:f,behavior:u})}else n.scrollToIndex(a,{align:f,behavior:u})}))},this.scrollBy=function(a,r){var t=r===void 0?{}:r,l=t.behavior;n.cancelScrollToIndex(),l==="smooth"&&n.isDynamicMode()&&console.warn("The `smooth` scroll behavior is not fully supported with dynamic size."),n._scrollToOffset(n.scrollOffset+a,{adjustments:void 0,behavior:l})},this.getTotalSize=function(){var a;return(((a=n.getMeasurements()[n.options.count-1])==null?void 0:a.end)||n.options.paddingStart)-n.options.scrollMargin+n.options.paddingEnd},this._scrollToOffset=function(a,r){var t=r.adjustments,l=r.behavior;n.options.scrollToFn(a,{behavior:l,adjustments:t},n)},this.measure=function(){n.itemSizeCache=new Map,n.notify()},this.setOptions(o),this.scrollRect=this.options.initialRect,this.scrollOffset=this.options.initialOffset,this.measurementsCache=this.options.initialMeasurementsCache,this.measurementsCache.forEach(function(a){n.itemSizeCache.set(a.key,a.size)}),this.maybeNotify()},en=function(o,n,a,r){for(;o<=n;){var t=(o+n)/2|0,l=a(t);if(l<r)o=t+1;else if(l>r)n=t-1;else return t}return o>0?o-1:0};function Do(e){for(var o=e.measurements,n=e.outerSize,a=e.scrollOffset,r=o.length-1,t=function(p){return o[p].start},l=en(0,r,t,a),i=l;i<r&&o[i].end<a+n;)i++;return{startIndex:l,endIndex:i}}var te=require("vue");function Lo(e){var o=new Zt((0,te.unref)(e)),n=(0,te.shallowRef)(o),a=o._didMount();return(0,te.watch)(function(){return(0,te.unref)(e).getScrollElement()},function(r){r&&o._willUpdate()},{immediate:!0}),(0,te.watch)(function(){return(0,te.unref)(e)},function(r){o.setOptions(We({},r,{onChange:function(l){(0,te.triggerRef)(n),r.onChange==null||r.onChange(l)}})),o._willUpdate(),(0,te.triggerRef)(n)},{immediate:!0}),(0,te.onScopeDispose)(a),n}function tn(e){return Lo((0,te.computed)(function(){return We({observeElementRect:Qt,observeElementOffset:Jt,scrollToFn:Xt},(0,te.unref)(e))}))}var E=require("vue");var Ge=require("vue");function ye(e,o,n){let a=(0,Ge.ref)(n==null?void 0:n.value),r=(0,Ge.computed)(()=>e.value!==void 0);return[(0,Ge.computed)(()=>r.value?e.value:a.value),function(t){return r.value||(a.value=t),o==null?void 0:o(t)}]}var nn=require("vue");function Ee(e){typeof queueMicrotask=="function"?queueMicrotask(e):Promise.resolve().then(e).catch(o=>setTimeout(()=>{throw o}))}function ie(){let e=[],o={addEventListener(n,a,r,t){return n.addEventListener(a,r,t),o.add(()=>n.removeEventListener(a,r,t))},requestAnimationFrame(...n){let a=requestAnimationFrame(...n);o.add(()=>cancelAnimationFrame(a))},nextFrame(...n){o.requestAnimationFrame(()=>{o.requestAnimationFrame(...n)})},setTimeout(...n){let a=setTimeout(...n);o.add(()=>clearTimeout(a))},microTask(...n){let a={current:!0};return Ee(()=>{a.current&&n[0]()}),o.add(()=>{a.current=!1})},style(n,a,r){let t=n.style.getPropertyValue(a);return Object.assign(n.style,{[a]:r}),this.add(()=>{Object.assign(n.style,{[a]:t})})},group(n){let a=ie();return n(a),this.add(()=>a.dispose())},add(n){return e.push(n),()=>{let a=e.indexOf(n);if(a>=0)for(let r of e.splice(a,1))r()}},dispose(){for(let n of e.splice(0))n()}};return o}function on(){let e=ie();return(0,nn.onUnmounted)(()=>e.dispose()),e}function rn(){let e=on();return o=>{e.dispose(),e.nextFrame(o)}}var Le=wo(require("vue"),1),an=Symbol("headlessui.useid"),Fo=0,ln,N=(ln=Le.useId)!=null?ln:function(){return Le.inject(an,()=>`${++Fo}`)()};function un(e){Le.provide(an,e)}var it=require("vue");function v(e){var n;if(e==null||e.value==null)return null;let o=(n=e.value.$el)!=null?n:e.value;return o instanceof Node?o:null}var sn=require("vue");function A(e,o,...n){if(e in o){let r=o[e];return typeof r=="function"?r(...n):r}let a=new Error(`Tried to handle "${e}" but there is no handler defined. Only defined handlers are: ${Object.keys(o).map(r=>`"${r}"`).join(", ")}.`);throw Error.captureStackTrace&&Error.captureStackTrace(a,A),a}var Mt=class{constructor(){It(this,"current",this.detect());It(this,"currentId",0)}set(o){this.current!==o&&(this.currentId=0,this.current=o)}reset(){this.set(this.detect())}nextId(){return++this.currentId}get isServer(){return this.current==="server"}get isClient(){return this.current==="client"}detect(){return typeof window=="undefined"||typeof document=="undefined"?"server":"client"}},pe=new Mt;function q(e){if(pe.isServer)return null;if(e instanceof Node)return e.ownerDocument;if(e!=null&&e.hasOwnProperty("value")){let o=v(e);if(o)return o.ownerDocument}return document}var Dt=["[contentEditable=true]","[tabindex]","a[href]","area[href]","button:not([disabled])","iframe","input:not([disabled])","select:not([disabled])","textarea:not([disabled])"].map(e=>`${e}:not([tabindex='-1'])`).join(",");function Fe(e=document.body){return e==null?[]:Array.from(e.querySelectorAll(Dt)).sort((o,n)=>Math.sign((o.tabIndex||Number.MAX_SAFE_INTEGER)-(n.tabIndex||Number.MAX_SAFE_INTEGER)))}function be(e,o=0){var n;return e===((n=q(e))==null?void 0:n.body)?!1:A(o,{[0](){return e.matches(Dt)},[1](){let a=e;for(;a!==null;){if(a.matches(Dt))return!0;a=a.parentElement}return!1}})}function Lt(e){let o=q(e);(0,sn.nextTick)(()=>{o&&!be(o.activeElement,0)&&ge(e)})}typeof window!="undefined"&&typeof document!="undefined"&&(document.addEventListener("keydown",e=>{e.metaKey||e.altKey||e.ctrlKey||(document.documentElement.dataset.headlessuiFocusVisible="")},!0),document.addEventListener("click",e=>{e.detail===1?delete document.documentElement.dataset.headlessuiFocusVisible:e.detail===0&&(document.documentElement.dataset.headlessuiFocusVisible="")},!0));function ge(e){e==null||e.focus({preventScroll:!0})}var ko=["textarea","input"].join(",");function Ho(e){var o,n;return(n=(o=e==null?void 0:e.matches)==null?void 0:o.call(e,ko))!=null?n:!1}function re(e,o=n=>n){return e.slice().sort((n,a)=>{let r=o(n),t=o(a);if(r===null||t===null)return 0;let l=r.compareDocumentPosition(t);return l&Node.DOCUMENT_POSITION_FOLLOWING?-1:l&Node.DOCUMENT_POSITION_PRECEDING?1:0})}function dn(e,o){return Q(Fe(),o,{relativeTo:e})}function Q(e,o,{sorted:n=!0,relativeTo:a=null,skipElements:r=[]}={}){var S;let t=(S=Array.isArray(e)?e.length>0?e[0].ownerDocument:document:e==null?void 0:e.ownerDocument)!=null?S:document,l=Array.isArray(e)?n?re(e):e:Fe(e);r.length>0&&l.length>1&&(l=l.filter(y=>!r.includes(y))),a=a!=null?a:t.activeElement;let i=(()=>{if(o&5)return 1;if(o&10)return-1;throw new Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),u=(()=>{if(o&1)return 0;if(o&2)return Math.max(0,l.indexOf(a))-1;if(o&4)return Math.max(0,l.indexOf(a))+1;if(o&8)return l.length-1;throw new Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),p=o&32?{preventScroll:!0}:{},d=0,f=l.length,s;do{if(d>=f||d+f<=0)return 0;let y=u+d;if(o&16)y=(y+f)%f;else{if(y<0)return 3;if(y>=f)return 1}s=l[y],s==null||s.focus(p),d+=i}while(s!==t.activeElement);return o&6&&Ho(s)&&s.select(),2}function Ft(){return/iPhone/gi.test(window.navigator.platform)||/Mac/gi.test(window.navigator.platform)&&window.navigator.maxTouchPoints>0}function Ao(){return/Android/gi.test(window.navigator.userAgent)}function lt(){return Ft()||Ao()}var fn=require("vue");function qe(e,o,n){pe.isServer||(0,fn.watchEffect)(a=>{document.addEventListener(e,o,n),a(()=>document.removeEventListener(e,o,n))})}var cn=require("vue");function at(e,o,n){pe.isServer||(0,cn.watchEffect)(a=>{window.addEventListener(e,o,n),a(()=>window.removeEventListener(e,o,n))})}function me(e,o,n=(0,it.computed)(()=>!0)){function a(t,l){if(!n.value||t.defaultPrevented)return;let i=l(t);if(i===null||!i.getRootNode().contains(i))return;let u=function p(d){return typeof d=="function"?p(d()):Array.isArray(d)||d instanceof Set?d:[d]}(e);for(let p of u){if(p===null)continue;let d=p instanceof HTMLElement?p:v(p);if(d!=null&&d.contains(i)||t.composed&&t.composedPath().includes(d))return}return!be(i,1)&&i.tabIndex!==-1&&t.preventDefault(),o(t,i)}let r=(0,it.ref)(null);qe("pointerdown",t=>{var l,i;n.value&&(r.value=((i=(l=t.composedPath)==null?void 0:l.call(t))==null?void 0:i[0])||t.target)},!0),qe("mousedown",t=>{var l,i;n.value&&(r.value=((i=(l=t.composedPath)==null?void 0:l.call(t))==null?void 0:i[0])||t.target)},!0),qe("click",t=>{lt()||r.value&&(a(t,()=>r.value),r.value=null)},!0),qe("touchend",t=>a(t,()=>t.target instanceof HTMLElement?t.target:null),!0),at("blur",t=>a(t,()=>window.document.activeElement instanceof HTMLIFrameElement?window.document.activeElement:null),!0)}var ke=require("vue");function pn(e,o){if(e)return e;let n=o!=null?o:"button";if(typeof n=="string"&&n.toLowerCase()==="button")return"button"}function ae(e,o){let n=(0,ke.ref)(pn(e.value.type,e.value.as));return(0,ke.onMounted)(()=>{n.value=pn(e.value.type,e.value.as)}),(0,ke.watchEffect)(()=>{var a;n.value||v(o)&&v(o)instanceof HTMLButtonElement&&!((a=v(o))!=null&&a.hasAttribute("type"))&&(n.value="button")}),n}var vn=require("vue");function mn(e){return[e.screenX,e.screenY]}function He(){let e=(0,vn.ref)([-1,-1]);return{wasMoved(o){let n=mn(o);return e.value[0]===n[0]&&e.value[1]===n[1]?!1:(e.value=n,!0)},update(o){e.value=mn(o)}}}var bn=require("vue");function Ae({container:e,accept:o,walk:n,enabled:a}){(0,bn.watchEffect)(()=>{let r=e.value;if(!r||a!==void 0&&!a.value)return;let t=q(e);if(!t)return;let l=Object.assign(u=>o(u),{acceptNode:o}),i=t.createTreeWalker(r,NodeFilter.SHOW_ELEMENT,l,!1);for(;i.nextNode();)n(i.currentNode)})}var yn=require("vue");var je=require("vue");function D({visible:e=!0,features:o=0,ourProps:n,theirProps:a,...r}){var i;let t=hn(a,n),l=Object.assign(r,{props:t});if(e||o&2&&t.static)return ut(l);if(o&1){let u=(i=t.unmount)==null||i?0:1;return A(u,{[0](){return null},[1](){return ut({...r,props:{...t,hidden:!0,style:{display:"none"}}})}})}return ut(l)}function ut({props:e,attrs:o,slots:n,slot:a,name:r}){var p,d;let{as:t,...l}=le(e,["unmount","static"]),i=(p=n.default)==null?void 0:p.call(n,a),u={};if(a){let f=!1,s=[];for(let[S,y]of Object.entries(a))typeof y=="boolean"&&(f=!0),y===!0&&s.push(S);f&&(u["data-headlessui-state"]=s.join(" "))}if(t==="template"){if(i=gn(i!=null?i:[]),Object.keys(l).length>0||Object.keys(o).length>0){let[f,...s]=i!=null?i:[];if(!jo(f)||s.length>0)throw new Error(['Passing props on "template"!',"",`The current component <${r} /> is rendering a "template".`,"However we need to passthrough the following props:",Object.keys(l).concat(Object.keys(o)).map(b=>b.trim()).filter((b,g,T)=>T.indexOf(b)===g).sort((b,g)=>b.localeCompare(g)).map(b=>`  - ${b}`).join(`
`),"","You can apply a few solutions:",['Add an `as="..."` prop, to ensure that we render an actual element instead of a "template".',"Render a single element as the child so that we can forward the props onto that element."].map(b=>`  - ${b}`).join(`
`)].join(`
`));let S=hn((d=f.props)!=null?d:{},l,u),y=(0,je.cloneVNode)(f,S,!0);for(let b in S)b.startsWith("on")&&(y.props||(y.props={}),y.props[b]=S[b]);return y}return Array.isArray(i)&&i.length===1?i[0]:i}return(0,je.h)(t,Object.assign({},l,u),{default:()=>i})}function gn(e){return e.flatMap(o=>o.type===je.Fragment?gn(o.children):[o])}function hn(...e){var a;if(e.length===0)return{};if(e.length===1)return e[0];let o={},n={};for(let r of e)for(let t in r)t.startsWith("on")&&typeof r[t]=="function"?((a=n[t])!=null||(n[t]=[]),n[t].push(r[t])):o[t]=r[t];if(o.disabled||o["aria-disabled"])return Object.assign(o,Object.fromEntries(Object.keys(n).map(r=>[r,void 0])));for(let r in n)Object.assign(o,{[r](t,...l){let i=n[r];for(let u of i){if(t instanceof Event&&t.defaultPrevented)return;u(t,...l)}}});return o}function Se(e){let o=Object.assign({},e);for(let n in o)o[n]===void 0&&delete o[n];return o}function le(e,o=[]){let n=Object.assign({},e);for(let a of o)a in n&&delete n[a];return n}function jo(e){return e==null?!1:typeof e.type=="string"||typeof e.type=="object"||typeof e.type=="function"}var X=(0,yn.defineComponent)({name:"Hidden",props:{as:{type:[Object,String],default:"div"},features:{type:Number,default:1}},setup(e,{slots:o,attrs:n}){return()=>{var l;let{features:a,...r}=e,t={"aria-hidden":(a&2)===2?!0:(l=r["aria-hidden"])!=null?l:void 0,hidden:(a&4)===4?!0:void 0,style:{position:"fixed",top:1,left:1,width:1,height:0,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0",...(a&4)===4&&(a&2)!==2&&{display:"none"}}};return D({ourProps:t,theirProps:r,slot:{},attrs:n,slots:o,name:"Hidden"})}}});var st=require("vue"),Sn=Symbol("Context");function xn(){return ne()!==null}function ne(){return(0,st.inject)(Sn,null)}function se(e){(0,st.provide)(Sn,e)}function Tn(e){function o(){document.readyState!=="loading"&&(e(),document.removeEventListener("DOMContentLoaded",o))}typeof window!="undefined"&&typeof document!="undefined"&&(document.addEventListener("DOMContentLoaded",o),o())}var de=[];Tn(()=>{function e(o){o.target instanceof HTMLElement&&o.target!==document.body&&de[0]!==o.target&&(de.unshift(o.target),de=de.filter(n=>n!=null&&n.isConnected),de.splice(10))}window.addEventListener("click",e,{capture:!0}),window.addEventListener("mousedown",e,{capture:!0}),window.addEventListener("focus",e,{capture:!0}),document.body.addEventListener("click",e,{capture:!0}),document.body.addEventListener("mousedown",e,{capture:!0}),document.body.addEventListener("focus",e,{capture:!0})});function No(e){throw new Error("Unexpected object: "+e)}function Re(e,o){let n=o.resolveItems();if(n.length<=0)return null;let a=o.resolveActiveIndex(),r=a!=null?a:-1;switch(e.focus){case 0:{for(let t=0;t<n.length;++t)if(!o.resolveDisabled(n[t],t,n))return t;return a}case 1:{r===-1&&(r=n.length);for(let t=r-1;t>=0;--t)if(!o.resolveDisabled(n[t],t,n))return t;return a}case 2:{for(let t=r+1;t<n.length;++t)if(!o.resolveDisabled(n[t],t,n))return t;return a}case 3:{for(let t=n.length-1;t>=0;--t)if(!o.resolveDisabled(n[t],t,n))return t;return a}case 4:{for(let t=0;t<n.length;++t)if(o.resolveId(n[t],t,n)===e.id)return t;return a}case 5:return null;default:No(e)}}function Oe(e={},o=null,n=[]){for(let[a,r]of Object.entries(e))Rn(n,En(o,a),r);return n}function En(e,o){return e?e+"["+o+"]":o}function Rn(e,o,n){if(Array.isArray(n))for(let[a,r]of n.entries())Rn(e,En(o,a.toString()),r);else n instanceof Date?e.push([o,n.toISOString()]):typeof n=="boolean"?e.push([o,n?"1":"0"]):typeof n=="string"?e.push([o,n]):typeof n=="number"?e.push([o,`${n}`]):n==null?e.push([o,""]):Oe(n,o,e)}function dt(e){var n,a;let o=(n=e==null?void 0:e.form)!=null?n:e.closest("form");if(o){for(let r of o.elements)if(r!==e&&(r.tagName==="INPUT"&&r.type==="submit"||r.tagName==="BUTTON"&&r.type==="submit"||r.nodeName==="INPUT"&&r.type==="image")){r.click();return}(a=o.requestSubmit)==null||a.call(o)}}function Bo(e,o){return e===o}var wn=Symbol("ComboboxContext");function Pe(e){let o=(0,E.inject)(wn,null);if(o===null){let n=new Error(`<${e} /> is missing a parent <Combobox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(n,Pe),n}return o}var Cn=Symbol("VirtualContext"),Vo=(0,E.defineComponent)({name:"VirtualProvider",setup(e,{slots:o}){let n=Pe("VirtualProvider"),a=(0,E.computed)(()=>{let i=v(n.optionsRef);if(!i)return{start:0,end:0};let u=window.getComputedStyle(i);return{start:parseFloat(u.paddingBlockStart||u.paddingTop),end:parseFloat(u.paddingBlockEnd||u.paddingBottom)}}),r=tn((0,E.computed)(()=>({scrollPaddingStart:a.value.start,scrollPaddingEnd:a.value.end,count:n.virtual.value.options.length,estimateSize(){return 40},getScrollElement(){return v(n.optionsRef)},overscan:12}))),t=(0,E.computed)(()=>{var i;return(i=n.virtual.value)==null?void 0:i.options}),l=(0,E.ref)(0);return(0,E.watch)([t],()=>{l.value+=1}),(0,E.provide)(Cn,n.virtual.value?r:null),()=>[(0,E.h)("div",{style:{position:"relative",width:"100%",height:`${r.value.getTotalSize()}px`},ref:i=>{if(i){if(typeof process!="undefined"&&process.env.JEST_WORKER_ID!==void 0||n.activationTrigger.value===0)return;n.activeOptionIndex.value!==null&&n.virtual.value.options.length>n.activeOptionIndex.value&&r.value.scrollToIndex(n.activeOptionIndex.value)}}},r.value.getVirtualItems().map(i=>(0,E.cloneVNode)(o.default({option:n.virtual.value.options[i.index],open:n.comboboxState.value===0})[0],{key:`${l.value}-${i.index}`,"data-index":i.index,"aria-setsize":n.virtual.value.options.length,"aria-posinset":i.index+1,style:{position:"absolute",top:0,left:0,transform:`translateY(${i.start}px)`,overflowAnchor:"none"}})))]}}),Ko=(0,E.defineComponent)({name:"Combobox",emits:{"update:modelValue":e=>!0},props:{as:{type:[Object,String],default:"template"},disabled:{type:[Boolean],default:!1},by:{type:[String,Function],nullable:!0,default:null},modelValue:{type:[Object,String,Number,Boolean],default:void 0},defaultValue:{type:[Object,String,Number,Boolean],default:void 0},form:{type:String,optional:!0},name:{type:String,optional:!0},nullable:{type:Boolean,default:!1},multiple:{type:[Boolean],default:!1},immediate:{type:[Boolean],default:!1},virtual:{type:Object,default:null}},inheritAttrs:!1,setup(e,{slots:o,attrs:n,emit:a}){let r=(0,E.ref)(1),t=(0,E.ref)(null),l=(0,E.ref)(null),i=(0,E.ref)(null),u=(0,E.ref)(null),p=(0,E.ref)({static:!1,hold:!1}),d=(0,E.ref)([]),f=(0,E.ref)(null),s=(0,E.ref)(2),S=(0,E.ref)(!1);function y(w=I=>I){let I=f.value!==null?d.value[f.value]:null,H=w(d.value.slice()),k=H.length>0&&H[0].dataRef.order.value!==null?H.sort(($,J)=>$.dataRef.order.value-J.dataRef.order.value):re(H,$=>v($.dataRef.domRef)),W=I?k.indexOf(I):null;return W===-1&&(W=null),{options:k,activeOptionIndex:W}}let b=(0,E.computed)(()=>e.multiple?1:0),g=(0,E.computed)(()=>e.nullable),[T,m]=ye((0,E.computed)(()=>e.modelValue),w=>a("update:modelValue",w),(0,E.computed)(()=>e.defaultValue)),h=(0,E.computed)(()=>T.value===void 0?A(b.value,{[1]:[],[0]:void 0}):T.value),x=null,c=null;function O(w){return A(b.value,{[0](){return m==null?void 0:m(w)},[1]:()=>{let I=(0,E.toRaw)(R.value.value).slice(),H=(0,E.toRaw)(w),k=I.findIndex(W=>R.compare(H,(0,E.toRaw)(W)));return k===-1?I.push(H):I.splice(k,1),m==null?void 0:m(I)}})}let C=(0,E.computed)(()=>{});(0,E.watch)([C],([w],[I])=>{if(R.virtual.value&&w&&I&&f.value!==null){let H=w.indexOf(I[f.value]);H!==-1?f.value=H:f.value=null}});let R={comboboxState:r,value:h,mode:b,compare(w,I){if(typeof e.by=="string"){let H=e.by;return(w==null?void 0:w[H])===(I==null?void 0:I[H])}return e.by===null?Bo(w,I):e.by(w,I)},calculateIndex(w){return R.virtual.value?e.by===null?R.virtual.value.options.indexOf(w):R.virtual.value.options.findIndex(I=>R.compare(I,w)):d.value.findIndex(I=>R.compare(I.dataRef.value,w))},defaultValue:(0,E.computed)(()=>e.defaultValue),nullable:g,immediate:(0,E.computed)(()=>!1),virtual:(0,E.computed)(()=>null),inputRef:l,labelRef:t,buttonRef:i,optionsRef:u,disabled:(0,E.computed)(()=>e.disabled),options:d,change(w){m(w)},activeOptionIndex:(0,E.computed)(()=>{if(S.value&&f.value===null&&(R.virtual.value?R.virtual.value.options.length>0:d.value.length>0)){if(R.virtual.value){let I=R.virtual.value.options.findIndex(H=>{var k;return!((k=R.virtual.value)!=null&&k.disabled(H))});if(I!==-1)return I}let w=d.value.findIndex(I=>!I.dataRef.disabled);if(w!==-1)return w}return f.value}),activationTrigger:s,optionsPropsRef:p,closeCombobox(){S.value=!1,!e.disabled&&r.value!==1&&(r.value=1,f.value=null)},openCombobox(){if(S.value=!0,!e.disabled&&r.value!==0){if(R.value.value){let w=R.calculateIndex(R.value.value);w!==-1&&(f.value=w)}r.value=0}},setActivationTrigger(w){s.value=w},goToOption(w,I,H){S.value=!1,x!==null&&cancelAnimationFrame(x),x=requestAnimationFrame(()=>{if(e.disabled||u.value&&!p.value.static&&r.value===1)return;if(R.virtual.value){f.value=w===4?I:Re({focus:w},{resolveItems:()=>R.virtual.value.options,resolveActiveIndex:()=>{var $,J;return(J=($=R.activeOptionIndex.value)!=null?$:R.virtual.value.options.findIndex(fe=>{var he;return!((he=R.virtual.value)!=null&&he.disabled(fe))}))!=null?J:null},resolveDisabled:$=>R.virtual.value.disabled($),resolveId(){throw new Error("Function not implemented.")}}),s.value=H!=null?H:2;return}let k=y();if(k.activeOptionIndex===null){let $=k.options.findIndex(J=>!J.dataRef.disabled);$!==-1&&(k.activeOptionIndex=$)}let W=w===4?I:Re({focus:w},{resolveItems:()=>k.options,resolveActiveIndex:()=>k.activeOptionIndex,resolveId:$=>$.id,resolveDisabled:$=>$.dataRef.disabled});f.value=W,s.value=H!=null?H:2,d.value=k.options})},selectOption(w){let I=d.value.find(k=>k.id===w);if(!I)return;let{dataRef:H}=I;O(H.value)},selectActiveOption(){if(R.activeOptionIndex.value!==null){if(R.virtual.value)O(R.virtual.value.options[R.activeOptionIndex.value]);else{let{dataRef:w}=d.value[R.activeOptionIndex.value];O(w.value)}R.goToOption(4,R.activeOptionIndex.value)}},registerOption(w,I){let H=(0,E.reactive)({id:w,dataRef:I});if(R.virtual.value){d.value.push(H);return}c&&cancelAnimationFrame(c);let k=y(W=>(W.push(H),W));f.value===null&&R.isSelected(I.value.value)&&(k.activeOptionIndex=k.options.indexOf(H)),d.value=k.options,f.value=k.activeOptionIndex,s.value=2,k.options.some(W=>!v(W.dataRef.domRef))&&(c=requestAnimationFrame(()=>{let W=y();d.value=W.options,f.value=W.activeOptionIndex}))},unregisterOption(w,I){if(x!==null&&cancelAnimationFrame(x),I&&(S.value=!0),R.virtual.value){d.value=d.value.filter(k=>k.id!==w);return}let H=y(k=>{let W=k.findIndex($=>$.id===w);return W!==-1&&k.splice(W,1),k});d.value=H.options,f.value=H.activeOptionIndex,s.value=2},isSelected(w){return A(b.value,{[0]:()=>R.compare((0,E.toRaw)(R.value.value),(0,E.toRaw)(w)),[1]:()=>(0,E.toRaw)(R.value.value).some(I=>R.compare((0,E.toRaw)(I),(0,E.toRaw)(w)))})},isActive(w){return f.value===R.calculateIndex(w)}};me([l,i,u],()=>R.closeCombobox(),(0,E.computed)(()=>r.value===0)),(0,E.provide)(wn,R),se((0,E.computed)(()=>A(r.value,{[0]:1,[1]:2})));let V=(0,E.computed)(()=>{var w;return(w=v(l))==null?void 0:w.closest("form")});return(0,E.onMounted)(()=>{(0,E.watch)([V],()=>{if(!V.value||e.defaultValue===void 0)return;function w(){R.change(e.defaultValue)}return V.value.addEventListener("reset",w),()=>{var I;(I=V.value)==null||I.removeEventListener("reset",w)}},{immediate:!0})}),()=>{var $,J,fe;let{name:w,disabled:I,form:H,...k}=e,W={open:r.value===0,disabled:I,activeIndex:R.activeOptionIndex.value,activeOption:R.activeOptionIndex.value===null?null:R.virtual.value?R.virtual.value.options[($=R.activeOptionIndex.value)!=null?$:0]:(fe=(J=R.options.value[R.activeOptionIndex.value])==null?void 0:J.dataRef.value)!=null?fe:null,value:h.value};return(0,E.h)(E.Fragment,[...w!=null&&h.value!=null?Oe({[w]:h.value}).map(([he,nt])=>(0,E.h)(X,Se({features:4,key:he,as:"input",type:"hidden",hidden:!0,readOnly:!0,form:H,disabled:I,name:he,value:nt}))):[],D({theirProps:{...n,...le(k,["by","defaultValue","immediate","modelValue","multiple","nullable","onUpdate:modelValue","virtual"])},ourProps:{},slot:W,slots:o,attrs:n,name:"Combobox"})])}}}),$o=(0,E.defineComponent)({name:"ComboboxLabel",props:{as:{type:[Object,String],default:"label"},id:{type:String,default:null}},setup(e,{attrs:o,slots:n}){var l;let a=(l=e.id)!=null?l:`headlessui-combobox-label-${N()}`,r=Pe("ComboboxLabel");function t(){var i;(i=v(r.inputRef))==null||i.focus({preventScroll:!0})}return()=>{let i={open:r.comboboxState.value===0,disabled:r.disabled.value},{...u}=e,p={id:a,ref:r.labelRef,onClick:t};return D({ourProps:p,theirProps:u,slot:i,attrs:o,slots:n,name:"ComboboxLabel"})}}}),_o=(0,E.defineComponent)({name:"ComboboxButton",props:{as:{type:[Object,String],default:"button"},id:{type:String,default:null}},setup(e,{attrs:o,slots:n,expose:a}){var p;let r=(p=e.id)!=null?p:`headlessui-combobox-button-${N()}`,t=Pe("ComboboxButton");a({el:t.buttonRef,$el:t.buttonRef});function l(d){t.disabled.value||(t.comboboxState.value===0?t.closeCombobox():(d.preventDefault(),t.openCombobox()),(0,E.nextTick)(()=>{var f;return(f=v(t.inputRef))==null?void 0:f.focus({preventScroll:!0})}))}function i(d){switch(d.key){case"ArrowDown":d.preventDefault(),d.stopPropagation(),t.comboboxState.value===1&&t.openCombobox(),(0,E.nextTick)(()=>{var f;return(f=t.inputRef.value)==null?void 0:f.focus({preventScroll:!0})});return;case"ArrowUp":d.preventDefault(),d.stopPropagation(),t.comboboxState.value===1&&(t.openCombobox(),(0,E.nextTick)(()=>{t.value.value||t.goToOption(3)})),(0,E.nextTick)(()=>{var f;return(f=t.inputRef.value)==null?void 0:f.focus({preventScroll:!0})});return;case"Escape":if(t.comboboxState.value!==0)return;d.preventDefault(),t.optionsRef.value&&!t.optionsPropsRef.value.static&&d.stopPropagation(),t.closeCombobox(),(0,E.nextTick)(()=>{var f;return(f=t.inputRef.value)==null?void 0:f.focus({preventScroll:!0})});return}}let u=ae((0,E.computed)(()=>({as:e.as,type:o.type})),t.buttonRef);return()=>{var S,y;let d={open:t.comboboxState.value===0,disabled:t.disabled.value,value:t.value.value},{...f}=e,s={ref:t.buttonRef,id:r,type:u.value,tabindex:"-1","aria-haspopup":"listbox","aria-controls":(S=v(t.optionsRef))==null?void 0:S.id,"aria-expanded":t.comboboxState.value===0,"aria-labelledby":t.labelRef.value?[(y=v(t.labelRef))==null?void 0:y.id,r].join(" "):void 0,disabled:t.disabled.value===!0?!0:void 0,onKeydown:i,onClick:l};return D({ourProps:s,theirProps:f,slot:d,attrs:o,slots:n,name:"ComboboxButton"})}}}),Uo=(0,E.defineComponent)({name:"ComboboxInput",props:{as:{type:[Object,String],default:"input"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},displayValue:{type:Function},defaultValue:{type:String,default:void 0},id:{type:String,default:null}},emits:{change:e=>!0},setup(e,{emit:o,attrs:n,slots:a,expose:r}){var x;let t=(x=e.id)!=null?x:`headlessui-combobox-input-${N()}`,l=Pe("ComboboxInput"),i=(0,E.computed)(()=>q(v(l.inputRef))),u={value:!1};r({el:l.inputRef,$el:l.inputRef});function p(){l.change(null);let c=v(l.optionsRef);c&&(c.scrollTop=0),l.goToOption(5)}let d=(0,E.computed)(()=>{var O;let c=l.value.value;return v(l.inputRef)?typeof e.displayValue!="undefined"&&c!==void 0?(O=e.displayValue(c))!=null?O:"":typeof c=="string"?c:"":""});(0,E.onMounted)(()=>{(0,E.watch)([d,l.comboboxState,i],([c,O],[C,R])=>{if(u.value)return;let V=v(l.inputRef);V&&((R===0&&O===1||c!==C)&&(V.value=c),requestAnimationFrame(()=>{var H;if(u.value||!V||((H=i.value)==null?void 0:H.activeElement)!==V)return;let{selectionStart:w,selectionEnd:I}=V;Math.abs((I!=null?I:0)-(w!=null?w:0))===0&&w===0&&V.setSelectionRange(V.value.length,V.value.length)}))},{immediate:!0}),(0,E.watch)([l.comboboxState],([c],[O])=>{if(c===0&&O===1){if(u.value)return;let C=v(l.inputRef);if(!C)return;let R=C.value,{selectionStart:V,selectionEnd:w,selectionDirection:I}=C;C.value="",C.value=R,I!==null?C.setSelectionRange(V,w,I):C.setSelectionRange(V,w)}})});let f=(0,E.ref)(!1);function s(){f.value=!0}function S(){ie().nextFrame(()=>{f.value=!1})}let y=rn();function b(c){switch(u.value=!0,y(()=>{u.value=!1}),c.key){case"Enter":if(u.value=!1,l.comboboxState.value!==0||f.value)return;if(c.preventDefault(),c.stopPropagation(),l.activeOptionIndex.value===null){l.closeCombobox();return}l.selectActiveOption(),l.mode.value===0&&l.closeCombobox();break;case"ArrowDown":return u.value=!1,c.preventDefault(),c.stopPropagation(),A(l.comboboxState.value,{[0]:()=>l.goToOption(2),[1]:()=>l.openCombobox()});case"ArrowUp":return u.value=!1,c.preventDefault(),c.stopPropagation(),A(l.comboboxState.value,{[0]:()=>l.goToOption(1),[1]:()=>{l.openCombobox(),(0,E.nextTick)(()=>{l.value.value||l.goToOption(3)})}});case"Home":if(c.shiftKey)break;return u.value=!1,c.preventDefault(),c.stopPropagation(),l.goToOption(0);case"PageUp":return u.value=!1,c.preventDefault(),c.stopPropagation(),l.goToOption(0);case"End":if(c.shiftKey)break;return u.value=!1,c.preventDefault(),c.stopPropagation(),l.goToOption(3);case"PageDown":return u.value=!1,c.preventDefault(),c.stopPropagation(),l.goToOption(3);case"Escape":if(u.value=!1,l.comboboxState.value!==0)return;c.preventDefault(),l.optionsRef.value&&!l.optionsPropsRef.value.static&&c.stopPropagation(),l.nullable.value&&l.mode.value===0&&l.value.value===null&&p(),l.closeCombobox();break;case"Tab":if(u.value=!1,l.comboboxState.value!==0)return;l.mode.value===0&&l.activationTrigger.value!==1&&l.selectActiveOption(),l.closeCombobox();break}}function g(c){o("change",c),l.nullable.value&&l.mode.value===0&&c.target.value===""&&p(),l.openCombobox()}function T(c){var C,R,V;let O=(C=c.relatedTarget)!=null?C:de.find(w=>w!==c.currentTarget);if(u.value=!1,!((R=v(l.optionsRef))!=null&&R.contains(O))&&!((V=v(l.buttonRef))!=null&&V.contains(O))&&l.comboboxState.value===0)return c.preventDefault(),l.mode.value===0&&(l.nullable.value&&l.value.value===null?p():l.activationTrigger.value!==1&&l.selectActiveOption()),l.closeCombobox()}function m(c){var C,R,V;let O=(C=c.relatedTarget)!=null?C:de.find(w=>w!==c.currentTarget);(R=v(l.buttonRef))!=null&&R.contains(O)||(V=v(l.optionsRef))!=null&&V.contains(O)||l.disabled.value||l.immediate.value&&l.comboboxState.value!==0&&(l.openCombobox(),ie().nextFrame(()=>{l.setActivationTrigger(1)}))}let h=(0,E.computed)(()=>{var c,O,C,R;return(R=(C=(O=e.defaultValue)!=null?O:l.defaultValue.value!==void 0?(c=e.displayValue)==null?void 0:c.call(e,l.defaultValue.value):null)!=null?C:l.defaultValue.value)!=null?R:""});return()=>{var w,I,H,k,W,$,J;let c={open:l.comboboxState.value===0},{displayValue:O,onChange:C,...R}=e,V={"aria-controls":(w=l.optionsRef.value)==null?void 0:w.id,"aria-expanded":l.comboboxState.value===0,"aria-activedescendant":l.activeOptionIndex.value===null?void 0:l.virtual.value?(I=l.options.value.find(fe=>!l.virtual.value.disabled(fe.dataRef.value)&&l.compare(fe.dataRef.value,l.virtual.value.options[l.activeOptionIndex.value])))==null?void 0:I.id:(H=l.options.value[l.activeOptionIndex.value])==null?void 0:H.id,"aria-labelledby":($=(k=v(l.labelRef))==null?void 0:k.id)!=null?$:(W=v(l.buttonRef))==null?void 0:W.id,"aria-autocomplete":"list",id:t,onCompositionstart:s,onCompositionend:S,onKeydown:b,onInput:g,onFocus:m,onBlur:T,role:"combobox",type:(J=n.type)!=null?J:"text",tabIndex:0,ref:l.inputRef,defaultValue:h.value,disabled:l.disabled.value===!0?!0:void 0};return D({ourProps:V,theirProps:R,slot:c,attrs:n,slots:a,features:3,name:"ComboboxInput"})}}}),Wo=(0,E.defineComponent)({name:"ComboboxOptions",props:{as:{type:[Object,String],default:"ul"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},hold:{type:[Boolean],default:!1}},setup(e,{attrs:o,slots:n,expose:a}){let r=Pe("ComboboxOptions"),t=`headlessui-combobox-options-${N()}`;a({el:r.optionsRef,$el:r.optionsRef}),(0,E.watchEffect)(()=>{r.optionsPropsRef.value.static=e.static}),(0,E.watchEffect)(()=>{r.optionsPropsRef.value.hold=e.hold});let l=ne(),i=(0,E.computed)(()=>l!==null?(l.value&1)===1:r.comboboxState.value===0);Ae({container:(0,E.computed)(()=>v(r.optionsRef)),enabled:(0,E.computed)(()=>r.comboboxState.value===0),accept(p){return p.getAttribute("role")==="option"?NodeFilter.FILTER_REJECT:p.hasAttribute("role")?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT},walk(p){p.setAttribute("role","none")}});function u(p){p.preventDefault()}return()=>{var s,S,y;let p={open:r.comboboxState.value===0},d={"aria-labelledby":(y=(s=v(r.labelRef))==null?void 0:s.id)!=null?y:(S=v(r.buttonRef))==null?void 0:S.id,id:t,ref:r.optionsRef,role:"listbox","aria-multiselectable":r.mode.value===1?!0:void 0,onMousedown:u},f=le(e,["hold"]);return D({ourProps:d,theirProps:f,slot:p,attrs:o,slots:r.virtual.value&&r.comboboxState.value===0?{...n,default:()=>[(0,E.h)(Vo,{},n.default)]}:n,features:3,visible:i.value,name:"ComboboxOptions"})}}}),zo=(0,E.defineComponent)({name:"ComboboxOption",props:{as:{type:[Object,String],default:"li"},value:{type:[Object,String,Number,Boolean]},disabled:{type:Boolean,default:!1},order:{type:[Number],default:null}},setup(e,{slots:o,attrs:n,expose:a}){let r=Pe("ComboboxOption"),t=`headlessui-combobox-option-${N()}`,l=(0,E.ref)(null),i=(0,E.computed)(()=>e.disabled);a({el:l,$el:l});let u=(0,E.computed)(()=>{var m;return r.virtual.value?r.activeOptionIndex.value===r.calculateIndex(e.value):r.activeOptionIndex.value===null?!1:((m=r.options.value[r.activeOptionIndex.value])==null?void 0:m.id)===t}),p=(0,E.computed)(()=>r.isSelected(e.value)),d=(0,E.inject)(Cn,null),f=(0,E.computed)(()=>({disabled:e.disabled,value:e.value,domRef:l,order:(0,E.computed)(()=>e.order)}));(0,E.onMounted)(()=>r.registerOption(t,f)),(0,E.onUnmounted)(()=>r.unregisterOption(t,u.value)),(0,E.watchEffect)(()=>{let m=v(l);m&&(d==null||d.value.measureElement(m))}),(0,E.watchEffect)(()=>{r.comboboxState.value===0&&u.value&&(r.virtual.value||r.activationTrigger.value!==0&&(0,E.nextTick)(()=>{var m,h;return(h=(m=v(l))==null?void 0:m.scrollIntoView)==null?void 0:h.call(m,{block:"nearest"})}))});function s(m){m.preventDefault(),m.button===0&&(i.value||(r.selectOption(t),lt()||requestAnimationFrame(()=>{var h;return(h=v(r.inputRef))==null?void 0:h.focus({preventScroll:!0})}),r.mode.value===0&&r.closeCombobox()))}function S(){var h;if(e.disabled||(h=r.virtual.value)!=null&&h.disabled(e.value))return r.goToOption(5);let m=r.calculateIndex(e.value);r.goToOption(4,m)}let y=He();function b(m){y.update(m)}function g(m){var x;if(!y.wasMoved(m)||e.disabled||(x=r.virtual.value)!=null&&x.disabled(e.value)||u.value)return;let h=r.calculateIndex(e.value);r.goToOption(4,h,0)}function T(m){var h;y.wasMoved(m)&&(e.disabled||(h=r.virtual.value)!=null&&h.disabled(e.value)||u.value&&(r.optionsPropsRef.value.hold||r.goToOption(5)))}return()=>{let{disabled:m}=e,h={active:u.value,selected:p.value,disabled:m},x={id:t,ref:l,role:"option",tabIndex:m===!0?void 0:-1,"aria-disabled":m===!0?!0:void 0,"aria-selected":p.value,disabled:void 0,onMousedown:s,onFocus:S,onPointerenter:b,onMouseenter:b,onPointermove:g,onMousemove:g,onPointerleave:T,onMouseleave:T},c=le(e,["order","value"]);return D({ourProps:x,theirProps:c,slot:h,attrs:n,slots:o,name:"ComboboxOption"})}}});var L=require("vue");var _=require("vue");var Pn=require("vue");function Be(e,o,n,a){pe.isServer||(0,Pn.watchEffect)(r=>{e=e!=null?e:window,e.addEventListener(o,n,a),r(()=>e.removeEventListener(o,n,a))})}var In=require("vue");function Ye(){let e=(0,In.ref)(0);return at("keydown",o=>{o.key==="Tab"&&(e.value=o.shiftKey?1:0)}),e}function Mn(e){if(!e)return new Set;if(typeof e=="function")return new Set(e());let o=new Set;for(let n of e.value){let a=v(n);a instanceof HTMLElement&&o.add(a)}return o}var Dn=(l=>(l[l.None=1]="None",l[l.InitialFocus=2]="InitialFocus",l[l.TabLock=4]="TabLock",l[l.FocusLock=8]="FocusLock",l[l.RestoreFocus=16]="RestoreFocus",l[l.All=30]="All",l))(Dn||{}),Ie=Object.assign((0,_.defineComponent)({name:"FocusTrap",props:{as:{type:[Object,String],default:"div"},initialFocus:{type:Object,default:null},features:{type:Number,default:30},containers:{type:[Object,Function],default:(0,_.ref)(new Set)}},inheritAttrs:!1,setup(e,{attrs:o,slots:n,expose:a}){let r=(0,_.ref)(null);a({el:r,$el:r});let t=(0,_.computed)(()=>q(r)),l=(0,_.ref)(!1);(0,_.onMounted)(()=>l.value=!0),(0,_.onUnmounted)(()=>l.value=!1),Yo({ownerDocument:t},(0,_.computed)(()=>l.value&&Boolean(e.features&16)));let i=Qo({ownerDocument:t,container:r,initialFocus:(0,_.computed)(()=>e.initialFocus)},(0,_.computed)(()=>l.value&&Boolean(e.features&2)));Jo({ownerDocument:t,container:r,containers:e.containers,previousActiveElement:i},(0,_.computed)(()=>l.value&&Boolean(e.features&8)));let u=Ye();function p(S){let y=v(r);if(!y)return;(g=>g())(()=>{A(u.value,{[0]:()=>{Q(y,1,{skipElements:[S.relatedTarget]})},[1]:()=>{Q(y,8,{skipElements:[S.relatedTarget]})}})})}let d=(0,_.ref)(!1);function f(S){S.key==="Tab"&&(d.value=!0,requestAnimationFrame(()=>{d.value=!1}))}function s(S){if(!l.value)return;let y=Mn(e.containers);v(r)instanceof HTMLElement&&y.add(v(r));let b=S.relatedTarget;b instanceof HTMLElement&&b.dataset.headlessuiFocusGuard!=="true"&&(Ln(y,b)||(d.value?Q(v(r),A(u.value,{[0]:()=>4,[1]:()=>2})|16,{relativeTo:S.target}):S.target instanceof HTMLElement&&ge(S.target)))}return()=>{let S={},y={ref:r,onKeydown:f,onFocusout:s},{features:b,initialFocus:g,containers:T,...m}=e;return(0,_.h)(_.Fragment,[Boolean(b&4)&&(0,_.h)(X,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:p,features:2}),D({ourProps:y,theirProps:{...o,...m},slot:S,attrs:o,slots:n,name:"FocusTrap"}),Boolean(b&4)&&(0,_.h)(X,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:p,features:2})])}}}),{features:Dn});function qo(e){let o=(0,_.ref)(de.slice());return(0,_.watch)([e],([n],[a])=>{a===!0&&n===!1?Ee(()=>{o.value.splice(0)}):a===!1&&n===!0&&(o.value=de.slice())},{flush:"post"}),()=>{var n;return(n=o.value.find(a=>a!=null&&a.isConnected))!=null?n:null}}function Yo({ownerDocument:e},o){let n=qo(o);(0,_.onMounted)(()=>{(0,_.watchEffect)(()=>{var a,r;o.value||((a=e.value)==null?void 0:a.activeElement)===((r=e.value)==null?void 0:r.body)&&ge(n())},{flush:"post"})}),(0,_.onUnmounted)(()=>{o.value&&ge(n())})}function Qo({ownerDocument:e,container:o,initialFocus:n},a){let r=(0,_.ref)(null),t=(0,_.ref)(!1);return(0,_.onMounted)(()=>t.value=!0),(0,_.onUnmounted)(()=>t.value=!1),(0,_.onMounted)(()=>{(0,_.watch)([o,n,a],(l,i)=>{if(l.every((p,d)=>(i==null?void 0:i[d])===p)||!a.value)return;let u=v(o);u&&Ee(()=>{var f,s;if(!t.value)return;let p=v(n),d=(f=e.value)==null?void 0:f.activeElement;if(p){if(p===d){r.value=d;return}}else if(u.contains(d)){r.value=d;return}p?ge(p):Q(u,33)===0&&console.warn("There are no focusable elements inside the <FocusTrap />"),r.value=(s=e.value)==null?void 0:s.activeElement})},{immediate:!0,flush:"post"})}),r}function Jo({ownerDocument:e,container:o,containers:n,previousActiveElement:a},r){var t;Be((t=e.value)==null?void 0:t.defaultView,"focus",l=>{if(!r.value)return;let i=Mn(n);v(o)instanceof HTMLElement&&i.add(v(o));let u=a.value;if(!u)return;let p=l.target;p&&p instanceof HTMLElement?Ln(i,p)?(a.value=p,ge(p)):(l.preventDefault(),l.stopPropagation(),ge(u)):ge(a.value)},!0)}function Ln(e,o){for(let n of e)if(n.contains(o))return!0;return!1}var pt=require("vue");var ct=require("vue");function Fn(e){let o=(0,ct.shallowRef)(e.getSnapshot());return(0,ct.onUnmounted)(e.subscribe(()=>{o.value=e.getSnapshot()})),o}function kn(e,o){let n=e(),a=new Set;return{getSnapshot(){return n},subscribe(r){return a.add(r),()=>a.delete(r)},dispatch(r,...t){let l=o[r].call(n,...t);l&&(n=l,a.forEach(i=>i()))}}}function Hn(){let e;return{before({doc:o}){var r;let n=o.documentElement;e=((r=o.defaultView)!=null?r:window).innerWidth-n.clientWidth},after({doc:o,d:n}){let a=o.documentElement,r=a.clientWidth-a.offsetWidth,t=e-r;n.style(a,"paddingRight",`${t}px`)}}}function An(){return Ft()?{before({doc:e,d:o,meta:n}){function a(r){return n.containers.flatMap(t=>t()).some(t=>t.contains(r))}o.microTask(()=>{var l;if(window.getComputedStyle(e.documentElement).scrollBehavior!=="auto"){let i=ie();i.style(e.documentElement,"scrollBehavior","auto"),o.add(()=>o.microTask(()=>i.dispose()))}let r=(l=window.scrollY)!=null?l:window.pageYOffset,t=null;o.addEventListener(e,"click",i=>{if(i.target instanceof HTMLElement)try{let u=i.target.closest("a");if(!u)return;let{hash:p}=new URL(u.href),d=e.querySelector(p);d&&!a(d)&&(t=d)}catch{}},!0),o.addEventListener(e,"touchstart",i=>{if(i.target instanceof HTMLElement)if(a(i.target)){let u=i.target;for(;u.parentElement&&a(u.parentElement);)u=u.parentElement;o.style(u,"overscrollBehavior","contain")}else o.style(i.target,"touchAction","none")}),o.addEventListener(e,"touchmove",i=>{if(i.target instanceof HTMLElement){if(i.target.tagName==="INPUT")return;if(a(i.target)){let u=i.target;for(;u.parentElement&&u.dataset.headlessuiPortal!==""&&!(u.scrollHeight>u.clientHeight||u.scrollWidth>u.clientWidth);)u=u.parentElement;u.dataset.headlessuiPortal===""&&i.preventDefault()}else i.preventDefault()}},{passive:!1}),o.add(()=>{var u;let i=(u=window.scrollY)!=null?u:window.pageYOffset;r!==i&&window.scrollTo(0,r),t&&t.isConnected&&(t.scrollIntoView({block:"nearest"}),t=null)})})}}:{}}function jn(){return{before({doc:e,d:o}){o.style(e.documentElement,"overflow","hidden")}}}function Xo(e){let o={};for(let n of e)Object.assign(o,n(o));return o}var xe=kn(()=>new Map,{PUSH(e,o){var a;let n=(a=this.get(e))!=null?a:{doc:e,count:0,d:ie(),meta:new Set};return n.count++,n.meta.add(o),this.set(e,n),this},POP(e,o){let n=this.get(e);return n&&(n.count--,n.meta.delete(o)),this},SCROLL_PREVENT({doc:e,d:o,meta:n}){let a={doc:e,d:o,meta:Xo(n)},r=[An(),Hn(),jn()];r.forEach(({before:t})=>t==null?void 0:t(a)),r.forEach(({after:t})=>t==null?void 0:t(a))},SCROLL_ALLOW({d:e}){e.dispose()},TEARDOWN({doc:e}){this.delete(e)}});xe.subscribe(()=>{let e=xe.getSnapshot(),o=new Map;for(let[n]of e)o.set(n,n.documentElement.style.overflow);for(let n of e.values()){let a=o.get(n.doc)==="hidden",r=n.count!==0;(r&&!a||!r&&a)&&xe.dispatch(n.count>0?"SCROLL_PREVENT":"SCROLL_ALLOW",n),n.count===0&&xe.dispatch("TEARDOWN",n)}});function Nn(e,o,n){let a=Fn(xe),r=(0,pt.computed)(()=>{let t=e.value?a.value.get(e.value):void 0;return t?t.count>0:!1});return(0,pt.watch)([e,o],([t,l],[i],u)=>{if(!t||!l)return;xe.dispatch("PUSH",t,n);let p=!1;u(()=>{p||(xe.dispatch("POP",i!=null?i:t,n),p=!0)})},{immediate:!0}),r}var mt=require("vue");var At=new Map,Qe=new Map;function jt(e,o=(0,mt.ref)(!0)){(0,mt.watchEffect)(n=>{var t;if(!o.value)return;let a=v(e);if(!a)return;n(function(){var p;if(!a)return;let i=(p=Qe.get(a))!=null?p:1;if(i===1?Qe.delete(a):Qe.set(a,i-1),i!==1)return;let u=At.get(a);u&&(u["aria-hidden"]===null?a.removeAttribute("aria-hidden"):a.setAttribute("aria-hidden",u["aria-hidden"]),a.inert=u.inert,At.delete(a))});let r=(t=Qe.get(a))!=null?t:0;Qe.set(a,r+1),r===0&&(At.set(a,{"aria-hidden":a.getAttribute("aria-hidden"),inert:a.inert}),a.setAttribute("aria-hidden","true"),a.inert=!0)})}var Ve=require("vue");function vt({defaultContainers:e=[],portals:o,mainTreeNodeRef:n}={}){let a=(0,Ve.ref)(null),r=q(a);function t(){var i,u,p;let l=[];for(let d of e)d!==null&&(d instanceof HTMLElement?l.push(d):"value"in d&&d.value instanceof HTMLElement&&l.push(d.value));if(o!=null&&o.value)for(let d of o.value)l.push(d);for(let d of(i=r==null?void 0:r.querySelectorAll("html > *, body > *"))!=null?i:[])d!==document.body&&d!==document.head&&d instanceof HTMLElement&&d.id!=="headlessui-portal-root"&&(d.contains(v(a))||d.contains((p=(u=v(a))==null?void 0:u.getRootNode())==null?void 0:p.host)||l.some(f=>d.contains(f))||l.push(d));return l}return{resolveContainers:t,contains(l){return t().some(i=>i.contains(l))},mainTreeNodeRef:a,MainTreeNode(){return n!=null?null:(0,Ve.h)(X,{features:4,ref:a})}}}function Bn(){let e=(0,Ve.ref)(null);return{mainTreeNodeRef:e,MainTreeNode(){return(0,Ve.h)(X,{features:4,ref:e})}}}var Ke=require("vue");var Vn=Symbol("ForcePortalRootContext");function Kn(){return(0,Ke.inject)(Vn,!1)}var bt=(0,Ke.defineComponent)({name:"ForcePortalRoot",props:{as:{type:[Object,String],default:"template"},force:{type:Boolean,default:!1}},setup(e,{slots:o,attrs:n}){return(0,Ke.provide)(Vn,e.force),()=>{let{force:a,...r}=e;return D({theirProps:r,ourProps:{},slot:{},slots:o,attrs:n,name:"ForcePortalRoot"})}}});var ve=require("vue"),$n=Symbol("StackContext");function Zo(){return(0,ve.inject)($n,()=>{})}function _n({type:e,enabled:o,element:n,onUpdate:a}){let r=Zo();function t(...l){a==null||a(...l),r(...l)}(0,ve.onMounted)(()=>{(0,ve.watch)(o,(l,i)=>{l?t(0,e,n):i===!0&&t(1,e,n)},{immediate:!0,flush:"sync"})}),(0,ve.onUnmounted)(()=>{o.value&&t(1,e,n)}),(0,ve.provide)($n,t)}var ee=require("vue");var Un=Symbol("DescriptionContext");function er(){let e=(0,ee.inject)(Un,null);if(e===null)throw new Error("Missing parent");return e}function Me({slot:e=(0,ee.ref)({}),name:o="Description",props:n={}}={}){let a=(0,ee.ref)([]);function r(t){return a.value.push(t),()=>{let l=a.value.indexOf(t);l!==-1&&a.value.splice(l,1)}}return(0,ee.provide)(Un,{register:r,slot:e,name:o,props:n}),(0,ee.computed)(()=>a.value.length>0?a.value.join(" "):void 0)}var $e=(0,ee.defineComponent)({name:"Description",props:{as:{type:[Object,String],default:"p"},id:{type:String,default:null}},setup(e,{attrs:o,slots:n}){var t;let a=(t=e.id)!=null?t:`headlessui-description-${N()}`,r=er();return(0,ee.onMounted)(()=>(0,ee.onUnmounted)(r.register(a))),()=>{let{name:l="Description",slot:i=(0,ee.ref)({}),props:u={}}=r,{...p}=e,d={...Object.entries(u).reduce((f,[s,S])=>Object.assign(f,{[s]:(0,ee.unref)(S)}),{}),id:a};return D({ourProps:d,theirProps:p,slot:i.value,attrs:o,slots:n,name:l})}}});var U=require("vue");function Wn(e){let o=q(e);if(!o){if(e===null)return null;throw new Error(`[Headless UI]: Cannot find ownerDocument for contextElement: ${e}`)}let n=o.getElementById("headlessui-portal-root");if(n)return n;let a=o.createElement("div");return a.setAttribute("id","headlessui-portal-root"),o.body.appendChild(a)}var Nt=new WeakMap;function tr(e){var o;return(o=Nt.get(e))!=null?o:0}function zn(e,o){let n=o(tr(e));return n<=0?Nt.delete(e):Nt.set(e,n),n}var Je=(0,U.defineComponent)({name:"Portal",props:{as:{type:[Object,String],default:"div"}},setup(e,{slots:o,attrs:n}){let a=(0,U.ref)(null),r=(0,U.computed)(()=>q(a)),t=Kn(),l=(0,U.inject)(Gn,null),i=(0,U.ref)(t===!0||l==null?Wn(a.value):l.resolveTarget());i.value&&zn(i.value,s=>s+1);let u=(0,U.ref)(!1);(0,U.onMounted)(()=>{u.value=!0}),(0,U.watchEffect)(()=>{t||l!=null&&(i.value=l.resolveTarget())});let p=(0,U.inject)(Bt,null),d=!1,f=(0,U.getCurrentInstance)();return(0,U.watch)(a,()=>{if(d||!p)return;let s=v(a);s&&((0,U.onUnmounted)(p.register(s),f),d=!0)}),(0,U.onUnmounted)(()=>{var y,b;let s=(y=r.value)==null?void 0:y.getElementById("headlessui-portal-root");!s||i.value!==s||zn(i.value,g=>g-1)||i.value.children.length>0||(b=i.value.parentElement)==null||b.removeChild(i.value)}),()=>{if(!u.value||i.value===null)return null;let s={ref:a,"data-headlessui-portal":""};return(0,U.h)(U.Teleport,{to:i.value},D({ourProps:s,theirProps:e,slot:{},attrs:n,slots:o,name:"Portal"}))}}}),Bt=Symbol("PortalParentContext");function gt(){let e=(0,U.inject)(Bt,null),o=(0,U.ref)([]);function n(t){return o.value.push(t),e&&e.register(t),()=>a(t)}function a(t){let l=o.value.indexOf(t);l!==-1&&o.value.splice(l,1),e&&e.unregister(t)}let r={register:n,unregister:a,portals:o};return[o,(0,U.defineComponent)({name:"PortalWrapper",setup(t,{slots:l}){return(0,U.provide)(Bt,r),()=>{var i;return(i=l.default)==null?void 0:i.call(l)}}})]}var Gn=Symbol("PortalGroupContext"),ht=(0,U.defineComponent)({name:"PortalGroup",props:{as:{type:[Object,String],default:"template"},target:{type:Object,default:null}},setup(e,{attrs:o,slots:n}){let a=(0,U.reactive)({resolveTarget(){return e.target}});return(0,U.provide)(Gn,a),()=>{let{target:r,...t}=e;return D({theirProps:t,ourProps:{},slot:{},attrs:o,slots:n,name:"PortalGroup"})}}});var Vt=Symbol("DialogContext");function Xe(e){let o=(0,L.inject)(Vt,null);if(o===null){let n=new Error(`<${e} /> is missing a parent <Dialog /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(n,Xe),n}return o}var yt="DC8F892D-2EBD-447C-A4C8-A03058436FF4",nr=(0,L.defineComponent)({name:"Dialog",inheritAttrs:!1,props:{as:{type:[Object,String],default:"div"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},open:{type:[Boolean,String],default:yt},initialFocus:{type:Object,default:null},id:{type:String,default:null},role:{type:String,default:"dialog"}},emits:{close:e=>!0},setup(e,{emit:o,attrs:n,slots:a,expose:r}){var wt,Ct;let t=(wt=e.id)!=null?wt:`headlessui-dialog-${N()}`,l=(0,L.ref)(!1);(0,L.onMounted)(()=>{l.value=!0});let i=!1,u=(0,L.computed)(()=>e.role==="dialog"||e.role==="alertdialog"?e.role:(i||(i=!0,console.warn(`Invalid role [${u}] passed to <Dialog />. Only \`dialog\` and and \`alertdialog\` are supported. Using \`dialog\` instead.`)),"dialog")),p=(0,L.ref)(0),d=ne(),f=(0,L.computed)(()=>e.open===yt&&d!==null?(d.value&1)===1:e.open),s=(0,L.ref)(null),S=(0,L.computed)(()=>q(s));if(r({el:s,$el:s}),!(e.open!==yt||d!==null))throw new Error("You forgot to provide an `open` prop to the `Dialog`.");if(typeof f.value!="boolean")throw new Error(`You provided an \`open\` prop to the \`Dialog\`, but the value is not a boolean. Received: ${f.value===yt?void 0:e.open}`);let b=(0,L.computed)(()=>l.value&&f.value?0:1),g=(0,L.computed)(()=>b.value===0),T=(0,L.computed)(()=>p.value>1),m=(0,L.inject)(Vt,null)!==null,[h,x]=gt(),{resolveContainers:c,mainTreeNodeRef:O,MainTreeNode:C}=vt({portals:h,defaultContainers:[(0,L.computed)(()=>{var G;return(G=J.panelRef.value)!=null?G:s.value})]}),R=(0,L.computed)(()=>T.value?"parent":"leaf"),V=(0,L.computed)(()=>d!==null?(d.value&4)===4:!1),w=(0,L.computed)(()=>m||V.value?!1:g.value),I=(0,L.computed)(()=>{var G,Z,ue;return(ue=Array.from((Z=(G=S.value)==null?void 0:G.querySelectorAll("body > *"))!=null?Z:[]).find(ce=>ce.id==="headlessui-portal-root"?!1:ce.contains(v(O))&&ce instanceof HTMLElement))!=null?ue:null});jt(I,w);let H=(0,L.computed)(()=>T.value?!0:g.value),k=(0,L.computed)(()=>{var G,Z,ue;return(ue=Array.from((Z=(G=S.value)==null?void 0:G.querySelectorAll("[data-headlessui-portal]"))!=null?Z:[]).find(ce=>ce.contains(v(O))&&ce instanceof HTMLElement))!=null?ue:null});jt(k,H),_n({type:"Dialog",enabled:(0,L.computed)(()=>b.value===0),element:s,onUpdate:(G,Z)=>{if(Z==="Dialog")return A(G,{[0]:()=>p.value+=1,[1]:()=>p.value-=1})}});let W=Me({name:"DialogDescription",slot:(0,L.computed)(()=>({open:f.value}))}),$=(0,L.ref)(null),J={titleId:$,panelRef:(0,L.ref)(null),dialogState:b,setTitleId(G){$.value!==G&&($.value=G)},close(){o("close",!1)}};(0,L.provide)(Vt,J);let fe=(0,L.computed)(()=>!(!g.value||T.value));me(c,(G,Z)=>{G.preventDefault(),J.close(),(0,L.nextTick)(()=>Z==null?void 0:Z.focus())},fe);let he=(0,L.computed)(()=>!(T.value||b.value!==0));Be((Ct=S.value)==null?void 0:Ct.defaultView,"keydown",G=>{he.value&&(G.defaultPrevented||G.key==="Escape"&&(G.preventDefault(),G.stopPropagation(),J.close()))});let nt=(0,L.computed)(()=>!(V.value||b.value!==0||m));return Nn(S,nt,G=>{var Z;return{containers:[...(Z=G.containers)!=null?Z:[],c]}}),(0,L.watchEffect)(G=>{if(b.value!==0)return;let Z=v(s);if(!Z)return;let ue=new ResizeObserver(ce=>{for(let Pt of ce){let ot=Pt.target.getBoundingClientRect();ot.x===0&&ot.y===0&&ot.width===0&&ot.height===0&&J.close()}});ue.observe(Z),G(()=>ue.disconnect())}),()=>{let{open:G,initialFocus:Z,...ue}=e,ce={...n,ref:s,id:t,role:u.value,"aria-modal":b.value===0?!0:void 0,"aria-labelledby":$.value,"aria-describedby":W.value},Pt={open:b.value===0};return(0,L.h)(bt,{force:!0},()=>[(0,L.h)(Je,()=>(0,L.h)(ht,{target:s.value},()=>(0,L.h)(bt,{force:!1},()=>(0,L.h)(Ie,{initialFocus:Z,containers:c,features:g.value?A(R.value,{parent:Ie.features.RestoreFocus,leaf:Ie.features.All&~Ie.features.FocusLock}):Ie.features.None},()=>(0,L.h)(x,{},()=>D({ourProps:ce,theirProps:{...ue,...n},slot:Pt,attrs:n,slots:a,visible:b.value===0,features:3,name:"Dialog"})))))),(0,L.h)(C)])}}}),or=(0,L.defineComponent)({name:"DialogOverlay",props:{as:{type:[Object,String],default:"div"},id:{type:String,default:null}},setup(e,{attrs:o,slots:n}){var l;let a=(l=e.id)!=null?l:`headlessui-dialog-overlay-${N()}`,r=Xe("DialogOverlay");function t(i){i.target===i.currentTarget&&(i.preventDefault(),i.stopPropagation(),r.close())}return()=>{let{...i}=e;return D({ourProps:{id:a,"aria-hidden":!0,onClick:t},theirProps:i,slot:{open:r.dialogState.value===0},attrs:o,slots:n,name:"DialogOverlay"})}}}),rr=(0,L.defineComponent)({name:"DialogBackdrop",props:{as:{type:[Object,String],default:"div"},id:{type:String,default:null}},inheritAttrs:!1,setup(e,{attrs:o,slots:n,expose:a}){var i;let r=(i=e.id)!=null?i:`headlessui-dialog-backdrop-${N()}`,t=Xe("DialogBackdrop"),l=(0,L.ref)(null);return a({el:l,$el:l}),(0,L.onMounted)(()=>{if(t.panelRef.value===null)throw new Error("A <DialogBackdrop /> component is being used, but a <DialogPanel /> component is missing.")}),()=>{let{...u}=e,p={id:r,ref:l,"aria-hidden":!0};return(0,L.h)(bt,{force:!0},()=>(0,L.h)(Je,()=>D({ourProps:p,theirProps:{...o,...u},slot:{open:t.dialogState.value===0},attrs:o,slots:n,name:"DialogBackdrop"})))}}}),lr=(0,L.defineComponent)({name:"DialogPanel",props:{as:{type:[Object,String],default:"div"},id:{type:String,default:null}},setup(e,{attrs:o,slots:n,expose:a}){var i;let r=(i=e.id)!=null?i:`headlessui-dialog-panel-${N()}`,t=Xe("DialogPanel");a({el:t.panelRef,$el:t.panelRef});function l(u){u.stopPropagation()}return()=>{let{...u}=e,p={id:r,ref:t.panelRef,onClick:l};return D({ourProps:p,theirProps:u,slot:{open:t.dialogState.value===0},attrs:o,slots:n,name:"DialogPanel"})}}}),ar=(0,L.defineComponent)({name:"DialogTitle",props:{as:{type:[Object,String],default:"h2"},id:{type:String,default:null}},setup(e,{attrs:o,slots:n}){var t;let a=(t=e.id)!=null?t:`headlessui-dialog-title-${N()}`,r=Xe("DialogTitle");return(0,L.onMounted)(()=>{r.setTitleId(a),(0,L.onUnmounted)(()=>r.setTitleId(null))}),()=>{let{...l}=e;return D({ourProps:{id:a},theirProps:l,slot:{open:r.dialogState.value===0},attrs:o,slots:n,name:"DialogTitle"})}}}),ir=$e;var z=require("vue");var qn=Symbol("DisclosureContext");function Kt(e){let o=(0,z.inject)(qn,null);if(o===null){let n=new Error(`<${e} /> is missing a parent <Disclosure /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(n,Kt),n}return o}var Yn=Symbol("DisclosurePanelContext");function ur(){return(0,z.inject)(Yn,null)}var sr=(0,z.defineComponent)({name:"Disclosure",props:{as:{type:[Object,String],default:"template"},defaultOpen:{type:[Boolean],default:!1}},setup(e,{slots:o,attrs:n}){let a=(0,z.ref)(e.defaultOpen?0:1),r=(0,z.ref)(null),t=(0,z.ref)(null),l={buttonId:(0,z.ref)(`headlessui-disclosure-button-${N()}`),panelId:(0,z.ref)(`headlessui-disclosure-panel-${N()}`),disclosureState:a,panel:r,button:t,toggleDisclosure(){a.value=A(a.value,{[0]:1,[1]:0})},closeDisclosure(){a.value!==1&&(a.value=1)},close(i){l.closeDisclosure();let u=(()=>i?i instanceof HTMLElement?i:i.value instanceof HTMLElement?v(i):v(l.button):v(l.button))();u==null||u.focus()}};return(0,z.provide)(qn,l),se((0,z.computed)(()=>A(a.value,{[0]:1,[1]:2}))),()=>{let{defaultOpen:i,...u}=e,p={open:a.value===0,close:l.close};return D({theirProps:u,ourProps:{},slot:p,slots:o,attrs:n,name:"Disclosure"})}}}),dr=(0,z.defineComponent)({name:"DisclosureButton",props:{as:{type:[Object,String],default:"button"},disabled:{type:[Boolean],default:!1},id:{type:String,default:null}},setup(e,{attrs:o,slots:n,expose:a}){let r=Kt("DisclosureButton"),t=ur(),l=(0,z.computed)(()=>t===null?!1:t.value===r.panelId.value);(0,z.onMounted)(()=>{l.value||e.id!==null&&(r.buttonId.value=e.id)}),(0,z.onUnmounted)(()=>{l.value||(r.buttonId.value=null)});let i=(0,z.ref)(null);a({el:i,$el:i}),l.value||(0,z.watchEffect)(()=>{r.button.value=i.value});let u=ae((0,z.computed)(()=>({as:e.as,type:o.type})),i);function p(){var s;e.disabled||(l.value?(r.toggleDisclosure(),(s=v(r.button))==null||s.focus()):r.toggleDisclosure())}function d(s){var S;if(!e.disabled)if(l.value)switch(s.key){case" ":case"Enter":s.preventDefault(),s.stopPropagation(),r.toggleDisclosure(),(S=v(r.button))==null||S.focus();break}else switch(s.key){case" ":case"Enter":s.preventDefault(),s.stopPropagation(),r.toggleDisclosure();break}}function f(s){switch(s.key){case" ":s.preventDefault();break}}return()=>{var g;let s={open:r.disclosureState.value===0},{id:S,...y}=e,b=l.value?{ref:i,type:u.value,onClick:p,onKeydown:d}:{id:(g=r.buttonId.value)!=null?g:S,ref:i,type:u.value,"aria-expanded":r.disclosureState.value===0,"aria-controls":r.disclosureState.value===0||v(r.panel)?r.panelId.value:void 0,disabled:e.disabled?!0:void 0,onClick:p,onKeydown:d,onKeyup:f};return D({ourProps:b,theirProps:y,slot:s,attrs:o,slots:n,name:"DisclosureButton"})}}}),fr=(0,z.defineComponent)({name:"DisclosurePanel",props:{as:{type:[Object,String],default:"div"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},id:{type:String,default:null}},setup(e,{attrs:o,slots:n,expose:a}){let r=Kt("DisclosurePanel");(0,z.onMounted)(()=>{e.id!==null&&(r.panelId.value=e.id)}),(0,z.onUnmounted)(()=>{r.panelId.value=null}),a({el:r.panel,$el:r.panel}),(0,z.provide)(Yn,r.panelId);let t=ne(),l=(0,z.computed)(()=>t!==null?(t.value&1)===1:r.disclosureState.value===0);return()=>{var f;let i={open:r.disclosureState.value===0,close:r.close},{id:u,...p}=e,d={id:(f=r.panelId.value)!=null?f:u,ref:r.panel};return D({ourProps:d,theirProps:p,slot:i,attrs:o,slots:n,features:3,visible:l.value,name:"DisclosurePanel"})}}});var P=require("vue");var $t=require("vue");var Qn=/([\u2700-\u27BF]|[\uE000-\uF8FF]|\uD83C[\uDC00-\uDFFF]|\uD83D[\uDC00-\uDFFF]|[\u2011-\u26FF]|\uD83E[\uDD10-\uDDFF])/g;function Jn(e){var t,l;let o=(t=e.innerText)!=null?t:"",n=e.cloneNode(!0);if(!(n instanceof HTMLElement))return o;let a=!1;for(let i of n.querySelectorAll('[hidden],[aria-hidden],[role="img"]'))i.remove(),a=!0;let r=a?(l=n.innerText)!=null?l:"":o;return Qn.test(r)&&(r=r.replace(Qn,"")),r}function Xn(e){let o=e.getAttribute("aria-label");if(typeof o=="string")return o.trim();let n=e.getAttribute("aria-labelledby");if(n){let a=n.split(" ").map(r=>{let t=document.getElementById(r);if(t){let l=t.getAttribute("aria-label");return typeof l=="string"?l.trim():Jn(t).trim()}return null}).filter(Boolean);if(a.length>0)return a.join(", ")}return Jn(e).trim()}function St(e){let o=(0,$t.ref)(""),n=(0,$t.ref)("");return()=>{let a=v(e);if(!a)return"";let r=a.innerText;if(o.value===r)return n.value;let t=Xn(a).trim().toLowerCase();return o.value=r,n.value=t,t}}function cr(e,o){return e===o}function pr(e){requestAnimationFrame(()=>requestAnimationFrame(e))}var Zn=Symbol("ListboxContext");function Ze(e){let o=(0,P.inject)(Zn,null);if(o===null){let n=new Error(`<${e} /> is missing a parent <Listbox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(n,Ze),n}return o}var mr=(0,P.defineComponent)({name:"Listbox",emits:{"update:modelValue":e=>!0},props:{as:{type:[Object,String],default:"template"},disabled:{type:[Boolean],default:!1},by:{type:[String,Function],default:()=>cr},horizontal:{type:[Boolean],default:!1},modelValue:{type:[Object,String,Number,Boolean],default:void 0},defaultValue:{type:[Object,String,Number,Boolean],default:void 0},form:{type:String,optional:!0},name:{type:String,optional:!0},multiple:{type:[Boolean],default:!1}},inheritAttrs:!1,setup(e,{slots:o,attrs:n,emit:a}){let r=(0,P.ref)(1),t=(0,P.ref)(null),l=(0,P.ref)(null),i=(0,P.ref)(null),u=(0,P.ref)([]),p=(0,P.ref)(""),d=(0,P.ref)(null),f=(0,P.ref)(1);function s(h=x=>x){let x=d.value!==null?u.value[d.value]:null,c=re(h(u.value.slice()),C=>v(C.dataRef.domRef)),O=x?c.indexOf(x):null;return O===-1&&(O=null),{options:c,activeOptionIndex:O}}let S=(0,P.computed)(()=>e.multiple?1:0),[y,b]=ye((0,P.computed)(()=>e.modelValue),h=>a("update:modelValue",h),(0,P.computed)(()=>e.defaultValue)),g=(0,P.computed)(()=>y.value===void 0?A(S.value,{[1]:[],[0]:void 0}):y.value),T={listboxState:r,value:g,mode:S,compare(h,x){if(typeof e.by=="string"){let c=e.by;return(h==null?void 0:h[c])===(x==null?void 0:x[c])}return e.by(h,x)},orientation:(0,P.computed)(()=>e.horizontal?"horizontal":"vertical"),labelRef:t,buttonRef:l,optionsRef:i,disabled:(0,P.computed)(()=>e.disabled),options:u,searchQuery:p,activeOptionIndex:d,activationTrigger:f,closeListbox(){e.disabled||r.value!==1&&(r.value=1,d.value=null)},openListbox(){e.disabled||r.value!==0&&(r.value=0)},goToOption(h,x,c){if(e.disabled||r.value===1)return;let O=s(),C=Re(h===4?{focus:4,id:x}:{focus:h},{resolveItems:()=>O.options,resolveActiveIndex:()=>O.activeOptionIndex,resolveId:R=>R.id,resolveDisabled:R=>R.dataRef.disabled});p.value="",d.value=C,f.value=c!=null?c:1,u.value=O.options},search(h){if(e.disabled||r.value===1)return;let c=p.value!==""?0:1;p.value+=h.toLowerCase();let C=(d.value!==null?u.value.slice(d.value+c).concat(u.value.slice(0,d.value+c)):u.value).find(V=>V.dataRef.textValue.startsWith(p.value)&&!V.dataRef.disabled),R=C?u.value.indexOf(C):-1;R===-1||R===d.value||(d.value=R,f.value=1)},clearSearch(){e.disabled||r.value!==1&&p.value!==""&&(p.value="")},registerOption(h,x){let c=s(O=>[...O,{id:h,dataRef:x}]);u.value=c.options,d.value=c.activeOptionIndex},unregisterOption(h){let x=s(c=>{let O=c.findIndex(C=>C.id===h);return O!==-1&&c.splice(O,1),c});u.value=x.options,d.value=x.activeOptionIndex,f.value=1},theirOnChange(h){e.disabled||b(h)},select(h){e.disabled||b(A(S.value,{[0]:()=>h,[1]:()=>{let x=(0,P.toRaw)(T.value.value).slice(),c=(0,P.toRaw)(h),O=x.findIndex(C=>T.compare(c,(0,P.toRaw)(C)));return O===-1?x.push(c):x.splice(O,1),x}}))}};me([l,i],(h,x)=>{var c;T.closeListbox(),be(x,1)||(h.preventDefault(),(c=v(l))==null||c.focus())},(0,P.computed)(()=>r.value===0)),(0,P.provide)(Zn,T),se((0,P.computed)(()=>A(r.value,{[0]:1,[1]:2})));let m=(0,P.computed)(()=>{var h;return(h=v(l))==null?void 0:h.closest("form")});return(0,P.onMounted)(()=>{(0,P.watch)([m],()=>{if(!m.value||e.defaultValue===void 0)return;function h(){T.theirOnChange(e.defaultValue)}return m.value.addEventListener("reset",h),()=>{var x;(x=m.value)==null||x.removeEventListener("reset",h)}},{immediate:!0})}),()=>{let{name:h,modelValue:x,disabled:c,form:O,...C}=e,R={open:r.value===0,disabled:c,value:g.value};return(0,P.h)(P.Fragment,[...h!=null&&g.value!=null?Oe({[h]:g.value}).map(([V,w])=>(0,P.h)(X,Se({features:4,key:V,as:"input",type:"hidden",hidden:!0,readOnly:!0,form:O,disabled:c,name:V,value:w}))):[],D({ourProps:{},theirProps:{...n,...le(C,["defaultValue","onUpdate:modelValue","horizontal","multiple","by"])},slot:R,slots:o,attrs:n,name:"Listbox"})])}}}),vr=(0,P.defineComponent)({name:"ListboxLabel",props:{as:{type:[Object,String],default:"label"},id:{type:String,default:null}},setup(e,{attrs:o,slots:n}){var l;let a=(l=e.id)!=null?l:`headlessui-listbox-label-${N()}`,r=Ze("ListboxLabel");function t(){var i;(i=v(r.buttonRef))==null||i.focus({preventScroll:!0})}return()=>{let i={open:r.listboxState.value===0,disabled:r.disabled.value},{...u}=e,p={id:a,ref:r.labelRef,onClick:t};return D({ourProps:p,theirProps:u,slot:i,attrs:o,slots:n,name:"ListboxLabel"})}}}),br=(0,P.defineComponent)({name:"ListboxButton",props:{as:{type:[Object,String],default:"button"},id:{type:String,default:null}},setup(e,{attrs:o,slots:n,expose:a}){var d;let r=(d=e.id)!=null?d:`headlessui-listbox-button-${N()}`,t=Ze("ListboxButton");a({el:t.buttonRef,$el:t.buttonRef});function l(f){switch(f.key){case" ":case"Enter":case"ArrowDown":f.preventDefault(),t.openListbox(),(0,P.nextTick)(()=>{var s;(s=v(t.optionsRef))==null||s.focus({preventScroll:!0}),t.value.value||t.goToOption(0)});break;case"ArrowUp":f.preventDefault(),t.openListbox(),(0,P.nextTick)(()=>{var s;(s=v(t.optionsRef))==null||s.focus({preventScroll:!0}),t.value.value||t.goToOption(3)});break}}function i(f){switch(f.key){case" ":f.preventDefault();break}}function u(f){t.disabled.value||(t.listboxState.value===0?(t.closeListbox(),(0,P.nextTick)(()=>{var s;return(s=v(t.buttonRef))==null?void 0:s.focus({preventScroll:!0})})):(f.preventDefault(),t.openListbox(),pr(()=>{var s;return(s=v(t.optionsRef))==null?void 0:s.focus({preventScroll:!0})})))}let p=ae((0,P.computed)(()=>({as:e.as,type:o.type})),t.buttonRef);return()=>{var y,b;let f={open:t.listboxState.value===0,disabled:t.disabled.value,value:t.value.value},{...s}=e,S={ref:t.buttonRef,id:r,type:p.value,"aria-haspopup":"listbox","aria-controls":(y=v(t.optionsRef))==null?void 0:y.id,"aria-expanded":t.listboxState.value===0,"aria-labelledby":t.labelRef.value?[(b=v(t.labelRef))==null?void 0:b.id,r].join(" "):void 0,disabled:t.disabled.value===!0?!0:void 0,onKeydown:l,onKeyup:i,onClick:u};return D({ourProps:S,theirProps:s,slot:f,attrs:o,slots:n,name:"ListboxButton"})}}}),gr=(0,P.defineComponent)({name:"ListboxOptions",props:{as:{type:[Object,String],default:"ul"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},id:{type:String,default:null}},setup(e,{attrs:o,slots:n,expose:a}){var d;let r=(d=e.id)!=null?d:`headlessui-listbox-options-${N()}`,t=Ze("ListboxOptions"),l=(0,P.ref)(null);a({el:t.optionsRef,$el:t.optionsRef});function i(f){switch(l.value&&clearTimeout(l.value),f.key){case" ":if(t.searchQuery.value!=="")return f.preventDefault(),f.stopPropagation(),t.search(f.key);case"Enter":if(f.preventDefault(),f.stopPropagation(),t.activeOptionIndex.value!==null){let s=t.options.value[t.activeOptionIndex.value];t.select(s.dataRef.value)}t.mode.value===0&&(t.closeListbox(),(0,P.nextTick)(()=>{var s;return(s=v(t.buttonRef))==null?void 0:s.focus({preventScroll:!0})}));break;case A(t.orientation.value,{vertical:"ArrowDown",horizontal:"ArrowRight"}):return f.preventDefault(),f.stopPropagation(),t.goToOption(2);case A(t.orientation.value,{vertical:"ArrowUp",horizontal:"ArrowLeft"}):return f.preventDefault(),f.stopPropagation(),t.goToOption(1);case"Home":case"PageUp":return f.preventDefault(),f.stopPropagation(),t.goToOption(0);case"End":case"PageDown":return f.preventDefault(),f.stopPropagation(),t.goToOption(3);case"Escape":f.preventDefault(),f.stopPropagation(),t.closeListbox(),(0,P.nextTick)(()=>{var s;return(s=v(t.buttonRef))==null?void 0:s.focus({preventScroll:!0})});break;case"Tab":f.preventDefault(),f.stopPropagation();break;default:f.key.length===1&&(t.search(f.key),l.value=setTimeout(()=>t.clearSearch(),350));break}}let u=ne(),p=(0,P.computed)(()=>u!==null?(u.value&1)===1:t.listboxState.value===0);return()=>{var y,b;let f={open:t.listboxState.value===0},{...s}=e,S={"aria-activedescendant":t.activeOptionIndex.value===null||(y=t.options.value[t.activeOptionIndex.value])==null?void 0:y.id,"aria-multiselectable":t.mode.value===1?!0:void 0,"aria-labelledby":(b=v(t.buttonRef))==null?void 0:b.id,"aria-orientation":t.orientation.value,id:r,onKeydown:i,role:"listbox",tabIndex:0,ref:t.optionsRef};return D({ourProps:S,theirProps:s,slot:f,attrs:o,slots:n,features:3,visible:p.value,name:"ListboxOptions"})}}}),hr=(0,P.defineComponent)({name:"ListboxOption",props:{as:{type:[Object,String],default:"li"},value:{type:[Object,String,Number,Boolean]},disabled:{type:Boolean,default:!1},id:{type:String,default:null}},setup(e,{slots:o,attrs:n,expose:a}){var m;let r=(m=e.id)!=null?m:`headlessui-listbox-option-${N()}`,t=Ze("ListboxOption"),l=(0,P.ref)(null);a({el:l,$el:l});let i=(0,P.computed)(()=>t.activeOptionIndex.value!==null?t.options.value[t.activeOptionIndex.value].id===r:!1),u=(0,P.computed)(()=>A(t.mode.value,{[0]:()=>t.compare((0,P.toRaw)(t.value.value),(0,P.toRaw)(e.value)),[1]:()=>(0,P.toRaw)(t.value.value).some(h=>t.compare((0,P.toRaw)(h),(0,P.toRaw)(e.value)))})),p=(0,P.computed)(()=>A(t.mode.value,{[1]:()=>{var x;let h=(0,P.toRaw)(t.value.value);return((x=t.options.value.find(c=>h.some(O=>t.compare((0,P.toRaw)(O),(0,P.toRaw)(c.dataRef.value)))))==null?void 0:x.id)===r},[0]:()=>u.value})),d=St(l),f=(0,P.computed)(()=>({disabled:e.disabled,value:e.value,get textValue(){return d()},domRef:l}));(0,P.onMounted)(()=>t.registerOption(r,f)),(0,P.onUnmounted)(()=>t.unregisterOption(r)),(0,P.onMounted)(()=>{(0,P.watch)([t.listboxState,u],()=>{t.listboxState.value===0&&u.value&&A(t.mode.value,{[1]:()=>{p.value&&t.goToOption(4,r)},[0]:()=>{t.goToOption(4,r)}})},{immediate:!0})}),(0,P.watchEffect)(()=>{t.listboxState.value===0&&i.value&&t.activationTrigger.value!==0&&(0,P.nextTick)(()=>{var h,x;return(x=(h=v(l))==null?void 0:h.scrollIntoView)==null?void 0:x.call(h,{block:"nearest"})})});function s(h){if(e.disabled)return h.preventDefault();t.select(e.value),t.mode.value===0&&(t.closeListbox(),(0,P.nextTick)(()=>{var x;return(x=v(t.buttonRef))==null?void 0:x.focus({preventScroll:!0})}))}function S(){if(e.disabled)return t.goToOption(5);t.goToOption(4,r)}let y=He();function b(h){y.update(h)}function g(h){y.wasMoved(h)&&(e.disabled||i.value||t.goToOption(4,r,0))}function T(h){y.wasMoved(h)&&(e.disabled||i.value&&t.goToOption(5))}return()=>{let{disabled:h}=e,x={active:i.value,selected:u.value,disabled:h},{value:c,disabled:O,...C}=e,R={id:r,ref:l,role:"option",tabIndex:h===!0?void 0:-1,"aria-disabled":h===!0?!0:void 0,"aria-selected":u.value,disabled:void 0,onClick:s,onFocus:S,onPointerenter:b,onMouseenter:b,onPointermove:g,onMousemove:g,onPointerleave:T,onMouseleave:T};return D({ourProps:R,theirProps:C,slot:x,attrs:n,slots:o,name:"ListboxOption"})}}});var K=require("vue");function yr(e){requestAnimationFrame(()=>requestAnimationFrame(e))}var eo=Symbol("MenuContext");function xt(e){let o=(0,K.inject)(eo,null);if(o===null){let n=new Error(`<${e} /> is missing a parent <Menu /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(n,xt),n}return o}var Sr=(0,K.defineComponent)({name:"Menu",props:{as:{type:[Object,String],default:"template"}},setup(e,{slots:o,attrs:n}){let a=(0,K.ref)(1),r=(0,K.ref)(null),t=(0,K.ref)(null),l=(0,K.ref)([]),i=(0,K.ref)(""),u=(0,K.ref)(null),p=(0,K.ref)(1);function d(s=S=>S){let S=u.value!==null?l.value[u.value]:null,y=re(s(l.value.slice()),g=>v(g.dataRef.domRef)),b=S?y.indexOf(S):null;return b===-1&&(b=null),{items:y,activeItemIndex:b}}let f={menuState:a,buttonRef:r,itemsRef:t,items:l,searchQuery:i,activeItemIndex:u,activationTrigger:p,closeMenu:()=>{a.value=1,u.value=null},openMenu:()=>a.value=0,goToItem(s,S,y){let b=d(),g=Re(s===4?{focus:4,id:S}:{focus:s},{resolveItems:()=>b.items,resolveActiveIndex:()=>b.activeItemIndex,resolveId:T=>T.id,resolveDisabled:T=>T.dataRef.disabled});i.value="",u.value=g,p.value=y!=null?y:1,l.value=b.items},search(s){let y=i.value!==""?0:1;i.value+=s.toLowerCase();let g=(u.value!==null?l.value.slice(u.value+y).concat(l.value.slice(0,u.value+y)):l.value).find(m=>m.dataRef.textValue.startsWith(i.value)&&!m.dataRef.disabled),T=g?l.value.indexOf(g):-1;T===-1||T===u.value||(u.value=T,p.value=1)},clearSearch(){i.value=""},registerItem(s,S){let y=d(b=>[...b,{id:s,dataRef:S}]);l.value=y.items,u.value=y.activeItemIndex,p.value=1},unregisterItem(s){let S=d(y=>{let b=y.findIndex(g=>g.id===s);return b!==-1&&y.splice(b,1),y});l.value=S.items,u.value=S.activeItemIndex,p.value=1}};return me([r,t],(s,S)=>{var y;f.closeMenu(),be(S,1)||(s.preventDefault(),(y=v(r))==null||y.focus())},(0,K.computed)(()=>a.value===0)),(0,K.provide)(eo,f),se((0,K.computed)(()=>A(a.value,{[0]:1,[1]:2}))),()=>{let s={open:a.value===0,close:f.closeMenu};return D({ourProps:{},theirProps:e,slot:s,slots:o,attrs:n,name:"Menu"})}}}),xr=(0,K.defineComponent)({name:"MenuButton",props:{disabled:{type:Boolean,default:!1},as:{type:[Object,String],default:"button"},id:{type:String,default:null}},setup(e,{attrs:o,slots:n,expose:a}){var d;let r=(d=e.id)!=null?d:`headlessui-menu-button-${N()}`,t=xt("MenuButton");a({el:t.buttonRef,$el:t.buttonRef});function l(f){switch(f.key){case" ":case"Enter":case"ArrowDown":f.preventDefault(),f.stopPropagation(),t.openMenu(),(0,K.nextTick)(()=>{var s;(s=v(t.itemsRef))==null||s.focus({preventScroll:!0}),t.goToItem(0)});break;case"ArrowUp":f.preventDefault(),f.stopPropagation(),t.openMenu(),(0,K.nextTick)(()=>{var s;(s=v(t.itemsRef))==null||s.focus({preventScroll:!0}),t.goToItem(3)});break}}function i(f){switch(f.key){case" ":f.preventDefault();break}}function u(f){e.disabled||(t.menuState.value===0?(t.closeMenu(),(0,K.nextTick)(()=>{var s;return(s=v(t.buttonRef))==null?void 0:s.focus({preventScroll:!0})})):(f.preventDefault(),t.openMenu(),yr(()=>{var s;return(s=v(t.itemsRef))==null?void 0:s.focus({preventScroll:!0})})))}let p=ae((0,K.computed)(()=>({as:e.as,type:o.type})),t.buttonRef);return()=>{var y;let f={open:t.menuState.value===0},{...s}=e,S={ref:t.buttonRef,id:r,type:p.value,"aria-haspopup":"menu","aria-controls":(y=v(t.itemsRef))==null?void 0:y.id,"aria-expanded":t.menuState.value===0,onKeydown:l,onKeyup:i,onClick:u};return D({ourProps:S,theirProps:s,slot:f,attrs:o,slots:n,name:"MenuButton"})}}}),Tr=(0,K.defineComponent)({name:"MenuItems",props:{as:{type:[Object,String],default:"div"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},id:{type:String,default:null}},setup(e,{attrs:o,slots:n,expose:a}){var f;let r=(f=e.id)!=null?f:`headlessui-menu-items-${N()}`,t=xt("MenuItems"),l=(0,K.ref)(null);a({el:t.itemsRef,$el:t.itemsRef}),Ae({container:(0,K.computed)(()=>v(t.itemsRef)),enabled:(0,K.computed)(()=>t.menuState.value===0),accept(s){return s.getAttribute("role")==="menuitem"?NodeFilter.FILTER_REJECT:s.hasAttribute("role")?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT},walk(s){s.setAttribute("role","none")}});function i(s){var S;switch(l.value&&clearTimeout(l.value),s.key){case" ":if(t.searchQuery.value!=="")return s.preventDefault(),s.stopPropagation(),t.search(s.key);case"Enter":if(s.preventDefault(),s.stopPropagation(),t.activeItemIndex.value!==null){let b=t.items.value[t.activeItemIndex.value];(S=v(b.dataRef.domRef))==null||S.click()}t.closeMenu(),Lt(v(t.buttonRef));break;case"ArrowDown":return s.preventDefault(),s.stopPropagation(),t.goToItem(2);case"ArrowUp":return s.preventDefault(),s.stopPropagation(),t.goToItem(1);case"Home":case"PageUp":return s.preventDefault(),s.stopPropagation(),t.goToItem(0);case"End":case"PageDown":return s.preventDefault(),s.stopPropagation(),t.goToItem(3);case"Escape":s.preventDefault(),s.stopPropagation(),t.closeMenu(),(0,K.nextTick)(()=>{var y;return(y=v(t.buttonRef))==null?void 0:y.focus({preventScroll:!0})});break;case"Tab":s.preventDefault(),s.stopPropagation(),t.closeMenu(),(0,K.nextTick)(()=>dn(v(t.buttonRef),s.shiftKey?2:4));break;default:s.key.length===1&&(t.search(s.key),l.value=setTimeout(()=>t.clearSearch(),350));break}}function u(s){switch(s.key){case" ":s.preventDefault();break}}let p=ne(),d=(0,K.computed)(()=>p!==null?(p.value&1)===1:t.menuState.value===0);return()=>{var b,g;let s={open:t.menuState.value===0},{...S}=e,y={"aria-activedescendant":t.activeItemIndex.value===null||(b=t.items.value[t.activeItemIndex.value])==null?void 0:b.id,"aria-labelledby":(g=v(t.buttonRef))==null?void 0:g.id,id:r,onKeydown:i,onKeyup:u,role:"menu",tabIndex:0,ref:t.itemsRef};return D({ourProps:y,theirProps:S,slot:s,attrs:o,slots:n,features:3,visible:d.value,name:"MenuItems"})}}}),Er=(0,K.defineComponent)({name:"MenuItem",inheritAttrs:!1,props:{as:{type:[Object,String],default:"template"},disabled:{type:Boolean,default:!1},id:{type:String,default:null}},setup(e,{slots:o,attrs:n,expose:a}){var g;let r=(g=e.id)!=null?g:`headlessui-menu-item-${N()}`,t=xt("MenuItem"),l=(0,K.ref)(null);a({el:l,$el:l});let i=(0,K.computed)(()=>t.activeItemIndex.value!==null?t.items.value[t.activeItemIndex.value].id===r:!1),u=St(l),p=(0,K.computed)(()=>({disabled:e.disabled,get textValue(){return u()},domRef:l}));(0,K.onMounted)(()=>t.registerItem(r,p)),(0,K.onUnmounted)(()=>t.unregisterItem(r)),(0,K.watchEffect)(()=>{t.menuState.value===0&&i.value&&t.activationTrigger.value!==0&&(0,K.nextTick)(()=>{var T,m;return(m=(T=v(l))==null?void 0:T.scrollIntoView)==null?void 0:m.call(T,{block:"nearest"})})});function d(T){if(e.disabled)return T.preventDefault();t.closeMenu(),Lt(v(t.buttonRef))}function f(){if(e.disabled)return t.goToItem(5);t.goToItem(4,r)}let s=He();function S(T){s.update(T)}function y(T){s.wasMoved(T)&&(e.disabled||i.value||t.goToItem(4,r,0))}function b(T){s.wasMoved(T)&&(e.disabled||i.value&&t.goToItem(5))}return()=>{let{disabled:T,...m}=e,h={active:i.value,disabled:T,close:t.closeMenu};return D({ourProps:{id:r,ref:l,role:"menuitem",tabIndex:T===!0?void 0:-1,"aria-disabled":T===!0?!0:void 0,onClick:d,onFocus:f,onPointerenter:S,onMouseenter:S,onPointermove:y,onMousemove:y,onPointerleave:b,onMouseleave:b},theirProps:{...n,...m},slot:h,attrs:n,slots:o,name:"MenuItem"})}}});var M=require("vue");var to=Symbol("PopoverContext");function Tt(e){let o=(0,M.inject)(to,null);if(o===null){let n=new Error(`<${e} /> is missing a parent <${lo.name} /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(n,Tt),n}return o}var no=Symbol("PopoverGroupContext");function oo(){return(0,M.inject)(no,null)}var ro=Symbol("PopoverPanelContext");function Rr(){return(0,M.inject)(ro,null)}var lo=(0,M.defineComponent)({name:"Popover",inheritAttrs:!1,props:{as:{type:[Object,String],default:"div"}},setup(e,{slots:o,attrs:n,expose:a}){var x;let r=(0,M.ref)(null);a({el:r,$el:r});let t=(0,M.ref)(1),l=(0,M.ref)(null),i=(0,M.ref)(null),u=(0,M.ref)(null),p=(0,M.ref)(null),d=(0,M.computed)(()=>q(r)),f=(0,M.computed)(()=>{var I,H;if(!v(l)||!v(p))return!1;for(let k of document.querySelectorAll("body > *"))if(Number(k==null?void 0:k.contains(v(l)))^Number(k==null?void 0:k.contains(v(p))))return!0;let c=Fe(),O=c.indexOf(v(l)),C=(O+c.length-1)%c.length,R=(O+1)%c.length,V=c[C],w=c[R];return!((I=v(p))!=null&&I.contains(V))&&!((H=v(p))!=null&&H.contains(w))}),s={popoverState:t,buttonId:(0,M.ref)(null),panelId:(0,M.ref)(null),panel:p,button:l,isPortalled:f,beforePanelSentinel:i,afterPanelSentinel:u,togglePopover(){t.value=A(t.value,{[0]:1,[1]:0})},closePopover(){t.value!==1&&(t.value=1)},close(c){s.closePopover();let O=(()=>c?c instanceof HTMLElement?c:c.value instanceof HTMLElement?v(c):v(s.button):v(s.button))();O==null||O.focus()}};(0,M.provide)(to,s),se((0,M.computed)(()=>A(t.value,{[0]:1,[1]:2})));let S={buttonId:s.buttonId,panelId:s.panelId,close(){s.closePopover()}},y=oo(),b=y==null?void 0:y.registerPopover,[g,T]=gt(),m=vt({mainTreeNodeRef:y==null?void 0:y.mainTreeNodeRef,portals:g,defaultContainers:[l,p]});function h(){var c,O,C,R;return(R=y==null?void 0:y.isFocusWithinPopoverGroup())!=null?R:((c=d.value)==null?void 0:c.activeElement)&&(((O=v(l))==null?void 0:O.contains(d.value.activeElement))||((C=v(p))==null?void 0:C.contains(d.value.activeElement)))}return(0,M.watchEffect)(()=>b==null?void 0:b(S)),Be((x=d.value)==null?void 0:x.defaultView,"focus",c=>{var O,C;c.target!==window&&c.target instanceof HTMLElement&&t.value===0&&(h()||l&&p&&(m.contains(c.target)||(O=v(s.beforePanelSentinel))!=null&&O.contains(c.target)||(C=v(s.afterPanelSentinel))!=null&&C.contains(c.target)||s.closePopover()))},!0),me(m.resolveContainers,(c,O)=>{var C;s.closePopover(),be(O,1)||(c.preventDefault(),(C=v(l))==null||C.focus())},(0,M.computed)(()=>t.value===0)),()=>{let c={open:t.value===0,close:s.close};return(0,M.h)(M.Fragment,[(0,M.h)(T,{},()=>D({theirProps:{...e,...n},ourProps:{ref:r},slot:c,slots:o,attrs:n,name:"Popover"})),(0,M.h)(m.MainTreeNode)])}}}),Or=(0,M.defineComponent)({name:"PopoverButton",props:{as:{type:[Object,String],default:"button"},disabled:{type:[Boolean],default:!1},id:{type:String,default:null}},inheritAttrs:!1,setup(e,{attrs:o,slots:n,expose:a}){var x;let r=(x=e.id)!=null?x:`headlessui-popover-button-${N()}`,t=Tt("PopoverButton"),l=(0,M.computed)(()=>q(t.button));a({el:t.button,$el:t.button}),(0,M.onMounted)(()=>{t.buttonId.value=r}),(0,M.onUnmounted)(()=>{t.buttonId.value=null});let i=oo(),u=i==null?void 0:i.closeOthers,p=Rr(),d=(0,M.computed)(()=>p===null?!1:p.value===t.panelId.value),f=(0,M.ref)(null),s=`headlessui-focus-sentinel-${N()}`;d.value||(0,M.watchEffect)(()=>{t.button.value=v(f)});let S=ae((0,M.computed)(()=>({as:e.as,type:o.type})),f);function y(c){var O,C,R,V,w;if(d.value){if(t.popoverState.value===1)return;switch(c.key){case" ":case"Enter":c.preventDefault(),(C=(O=c.target).click)==null||C.call(O),t.closePopover(),(R=v(t.button))==null||R.focus();break}}else switch(c.key){case" ":case"Enter":c.preventDefault(),c.stopPropagation(),t.popoverState.value===1&&(u==null||u(t.buttonId.value)),t.togglePopover();break;case"Escape":if(t.popoverState.value!==0)return u==null?void 0:u(t.buttonId.value);if(!v(t.button)||(V=l.value)!=null&&V.activeElement&&!((w=v(t.button))!=null&&w.contains(l.value.activeElement)))return;c.preventDefault(),c.stopPropagation(),t.closePopover();break}}function b(c){d.value||c.key===" "&&c.preventDefault()}function g(c){var O,C;e.disabled||(d.value?(t.closePopover(),(O=v(t.button))==null||O.focus()):(c.preventDefault(),c.stopPropagation(),t.popoverState.value===1&&(u==null||u(t.buttonId.value)),t.togglePopover(),(C=v(t.button))==null||C.focus()))}function T(c){c.preventDefault(),c.stopPropagation()}let m=Ye();function h(){let c=v(t.panel);if(!c)return;function O(){A(m.value,{[0]:()=>Q(c,1),[1]:()=>Q(c,8)})===0&&Q(Fe().filter(R=>R.dataset.headlessuiFocusGuard!=="true"),A(m.value,{[0]:4,[1]:2}),{relativeTo:v(t.button)})}O()}return()=>{let c=t.popoverState.value===0,O={open:c},{...C}=e,R=d.value?{ref:f,type:S.value,onKeydown:y,onClick:g}:{ref:f,id:r,type:S.value,"aria-expanded":t.popoverState.value===0,"aria-controls":v(t.panel)?t.panelId.value:void 0,disabled:e.disabled?!0:void 0,onKeydown:y,onKeyup:b,onClick:g,onMousedown:T};return(0,M.h)(M.Fragment,[D({ourProps:R,theirProps:{...o,...C},slot:O,attrs:o,slots:n,name:"PopoverButton"}),c&&!d.value&&t.isPortalled.value&&(0,M.h)(X,{id:s,features:2,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:h})])}}}),wr=(0,M.defineComponent)({name:"PopoverOverlay",props:{as:{type:[Object,String],default:"div"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0}},setup(e,{attrs:o,slots:n}){let a=Tt("PopoverOverlay"),r=`headlessui-popover-overlay-${N()}`,t=ne(),l=(0,M.computed)(()=>t!==null?(t.value&1)===1:a.popoverState.value===0);function i(){a.closePopover()}return()=>{let u={open:a.popoverState.value===0};return D({ourProps:{id:r,"aria-hidden":!0,onClick:i},theirProps:e,slot:u,attrs:o,slots:n,features:3,visible:l.value,name:"PopoverOverlay"})}}}),Cr=(0,M.defineComponent)({name:"PopoverPanel",props:{as:{type:[Object,String],default:"div"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},focus:{type:Boolean,default:!1},id:{type:String,default:null}},inheritAttrs:!1,setup(e,{attrs:o,slots:n,expose:a}){var T;let r=(T=e.id)!=null?T:`headlessui-popover-panel-${N()}`,{focus:t}=e,l=Tt("PopoverPanel"),i=(0,M.computed)(()=>q(l.panel)),u=`headlessui-focus-sentinel-before-${N()}`,p=`headlessui-focus-sentinel-after-${N()}`;a({el:l.panel,$el:l.panel}),(0,M.onMounted)(()=>{l.panelId.value=r}),(0,M.onUnmounted)(()=>{l.panelId.value=null}),(0,M.provide)(ro,l.panelId),(0,M.watchEffect)(()=>{var h,x;if(!t||l.popoverState.value!==0||!l.panel)return;let m=(h=i.value)==null?void 0:h.activeElement;(x=v(l.panel))!=null&&x.contains(m)||Q(v(l.panel),1)});let d=ne(),f=(0,M.computed)(()=>d!==null?(d.value&1)===1:l.popoverState.value===0);function s(m){var h,x;switch(m.key){case"Escape":if(l.popoverState.value!==0||!v(l.panel)||i.value&&!((h=v(l.panel))!=null&&h.contains(i.value.activeElement)))return;m.preventDefault(),m.stopPropagation(),l.closePopover(),(x=v(l.button))==null||x.focus();break}}function S(m){var x,c,O,C,R;let h=m.relatedTarget;h&&v(l.panel)&&((x=v(l.panel))!=null&&x.contains(h)||(l.closePopover(),((O=(c=v(l.beforePanelSentinel))==null?void 0:c.contains)!=null&&O.call(c,h)||(R=(C=v(l.afterPanelSentinel))==null?void 0:C.contains)!=null&&R.call(C,h))&&h.focus({preventScroll:!0})))}let y=Ye();function b(){let m=v(l.panel);if(!m)return;function h(){A(y.value,{[0]:()=>{var c;Q(m,1)===0&&((c=v(l.afterPanelSentinel))==null||c.focus())},[1]:()=>{var x;(x=v(l.button))==null||x.focus({preventScroll:!0})}})}h()}function g(){let m=v(l.panel);if(!m)return;function h(){A(y.value,{[0]:()=>{let x=v(l.button),c=v(l.panel);if(!x)return;let O=Fe(),C=O.indexOf(x),R=O.slice(0,C+1),w=[...O.slice(C+1),...R];for(let I of w.slice())if(I.dataset.headlessuiFocusGuard==="true"||c!=null&&c.contains(I)){let H=w.indexOf(I);H!==-1&&w.splice(H,1)}Q(w,1,{sorted:!1})},[1]:()=>{var c;Q(m,2)===0&&((c=v(l.button))==null||c.focus())}})}h()}return()=>{let m={open:l.popoverState.value===0,close:l.close},{focus:h,...x}=e,c={ref:l.panel,id:r,onKeydown:s,onFocusout:t&&l.popoverState.value===0?S:void 0,tabIndex:-1};return D({ourProps:c,theirProps:{...o,...x},attrs:o,slot:m,slots:{...n,default:(...O)=>{var C;return[(0,M.h)(M.Fragment,[f.value&&l.isPortalled.value&&(0,M.h)(X,{id:u,ref:l.beforePanelSentinel,features:2,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:b}),(C=n.default)==null?void 0:C.call(n,...O),f.value&&l.isPortalled.value&&(0,M.h)(X,{id:p,ref:l.afterPanelSentinel,features:2,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:g})])]}},features:3,visible:f.value,name:"PopoverPanel"})}}}),Pr=(0,M.defineComponent)({name:"PopoverGroup",inheritAttrs:!1,props:{as:{type:[Object,String],default:"div"}},setup(e,{attrs:o,slots:n,expose:a}){let r=(0,M.ref)(null),t=(0,M.shallowRef)([]),l=(0,M.computed)(()=>q(r)),i=Bn();a({el:r,$el:r});function u(s){let S=t.value.indexOf(s);S!==-1&&t.value.splice(S,1)}function p(s){return t.value.push(s),()=>{u(s)}}function d(){var y;let s=l.value;if(!s)return!1;let S=s.activeElement;return(y=v(r))!=null&&y.contains(S)?!0:t.value.some(b=>{var g,T;return((g=s.getElementById(b.buttonId.value))==null?void 0:g.contains(S))||((T=s.getElementById(b.panelId.value))==null?void 0:T.contains(S))})}function f(s){for(let S of t.value)S.buttonId.value!==s&&S.close()}return(0,M.provide)(no,{registerPopover:p,unregisterPopover:u,isFocusWithinPopoverGroup:d,closeOthers:f,mainTreeNodeRef:i.mainTreeNodeRef}),()=>(0,M.h)(M.Fragment,[D({ourProps:{ref:r},theirProps:{...e,...o},slot:{},attrs:o,slots:n,name:"PopoverGroup"}),(0,M.h)(i.MainTreeNode)])}});var j=require("vue");var oe=require("vue");var ao=Symbol("LabelContext");function io(){let e=(0,oe.inject)(ao,null);if(e===null){let o=new Error("You used a <Label /> component, but it is not inside a parent.");throw Error.captureStackTrace&&Error.captureStackTrace(o,io),o}return e}function et({slot:e={},name:o="Label",props:n={}}={}){let a=(0,oe.ref)([]);function r(t){return a.value.push(t),()=>{let l=a.value.indexOf(t);l!==-1&&a.value.splice(l,1)}}return(0,oe.provide)(ao,{register:r,slot:e,name:o,props:n}),(0,oe.computed)(()=>a.value.length>0?a.value.join(" "):void 0)}var Et=(0,oe.defineComponent)({name:"Label",props:{as:{type:[Object,String],default:"label"},passive:{type:[Boolean],default:!1},id:{type:String,default:null}},setup(e,{slots:o,attrs:n}){var t;let a=(t=e.id)!=null?t:`headlessui-label-${N()}`,r=io();return(0,oe.onMounted)(()=>(0,oe.onUnmounted)(r.register(a))),()=>{let{name:l="Label",slot:i={},props:u={}}=r,{passive:p,...d}=e,f={...Object.entries(u).reduce((s,[S,y])=>Object.assign(s,{[S]:(0,oe.unref)(y)}),{}),id:a};return p&&(delete f.onClick,delete f.htmlFor,delete d.onClick),D({ourProps:f,theirProps:d,slot:i,attrs:n,slots:o,name:l})}}});function Ir(e,o){return e===o}var uo=Symbol("RadioGroupContext");function so(e){let o=(0,j.inject)(uo,null);if(o===null){let n=new Error(`<${e} /> is missing a parent <RadioGroup /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(n,so),n}return o}var Mr=(0,j.defineComponent)({name:"RadioGroup",emits:{"update:modelValue":e=>!0},props:{as:{type:[Object,String],default:"div"},disabled:{type:[Boolean],default:!1},by:{type:[String,Function],default:()=>Ir},modelValue:{type:[Object,String,Number,Boolean],default:void 0},defaultValue:{type:[Object,String,Number,Boolean],default:void 0},form:{type:String,optional:!0},name:{type:String,optional:!0},id:{type:String,default:null}},inheritAttrs:!1,setup(e,{emit:o,attrs:n,slots:a,expose:r}){var b;let t=(b=e.id)!=null?b:`headlessui-radiogroup-${N()}`,l=(0,j.ref)(null),i=(0,j.ref)([]),u=et({name:"RadioGroupLabel"}),p=Me({name:"RadioGroupDescription"});r({el:l,$el:l});let[d,f]=ye((0,j.computed)(()=>e.modelValue),g=>o("update:modelValue",g),(0,j.computed)(()=>e.defaultValue)),s={options:i,value:d,disabled:(0,j.computed)(()=>e.disabled),firstOption:(0,j.computed)(()=>i.value.find(g=>!g.propsRef.disabled)),containsCheckedOption:(0,j.computed)(()=>i.value.some(g=>s.compare((0,j.toRaw)(g.propsRef.value),(0,j.toRaw)(e.modelValue)))),compare(g,T){if(typeof e.by=="string"){let m=e.by;return(g==null?void 0:g[m])===(T==null?void 0:T[m])}return e.by(g,T)},change(g){var m;if(e.disabled||s.compare((0,j.toRaw)(d.value),(0,j.toRaw)(g)))return!1;let T=(m=i.value.find(h=>s.compare((0,j.toRaw)(h.propsRef.value),(0,j.toRaw)(g))))==null?void 0:m.propsRef;return T!=null&&T.disabled?!1:(f(g),!0)},registerOption(g){i.value.push(g),i.value=re(i.value,T=>T.element)},unregisterOption(g){let T=i.value.findIndex(m=>m.id===g);T!==-1&&i.value.splice(T,1)}};(0,j.provide)(uo,s),Ae({container:(0,j.computed)(()=>v(l)),accept(g){return g.getAttribute("role")==="radio"?NodeFilter.FILTER_REJECT:g.hasAttribute("role")?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT},walk(g){g.setAttribute("role","none")}});function S(g){if(!l.value||!l.value.contains(g.target))return;let T=i.value.filter(m=>m.propsRef.disabled===!1).map(m=>m.element);switch(g.key){case"Enter":dt(g.currentTarget);break;case"ArrowLeft":case"ArrowUp":if(g.preventDefault(),g.stopPropagation(),Q(T,18)===2){let h=i.value.find(x=>{var c;return x.element===((c=q(l))==null?void 0:c.activeElement)});h&&s.change(h.propsRef.value)}break;case"ArrowRight":case"ArrowDown":if(g.preventDefault(),g.stopPropagation(),Q(T,20)===2){let h=i.value.find(x=>{var c;return x.element===((c=q(x.element))==null?void 0:c.activeElement)});h&&s.change(h.propsRef.value)}break;case" ":{g.preventDefault(),g.stopPropagation();let m=i.value.find(h=>{var x;return h.element===((x=q(h.element))==null?void 0:x.activeElement)});m&&s.change(m.propsRef.value)}break}}let y=(0,j.computed)(()=>{var g;return(g=v(l))==null?void 0:g.closest("form")});return(0,j.onMounted)(()=>{(0,j.watch)([y],()=>{if(!y.value||e.defaultValue===void 0)return;function g(){s.change(e.defaultValue)}return y.value.addEventListener("reset",g),()=>{var T;(T=y.value)==null||T.removeEventListener("reset",g)}},{immediate:!0})}),()=>{let{disabled:g,name:T,form:m,...h}=e,x={ref:l,id:t,role:"radiogroup","aria-labelledby":u.value,"aria-describedby":p.value,onKeydown:S};return(0,j.h)(j.Fragment,[...T!=null&&d.value!=null?Oe({[T]:d.value}).map(([c,O])=>(0,j.h)(X,Se({features:4,key:c,as:"input",type:"hidden",hidden:!0,readOnly:!0,form:m,disabled:g,name:c,value:O}))):[],D({ourProps:x,theirProps:{...n,...le(h,["modelValue","defaultValue","by"])},slot:{},attrs:n,slots:a,name:"RadioGroup"})])}}});var Dr=(0,j.defineComponent)({name:"RadioGroupOption",props:{as:{type:[Object,String],default:"div"},value:{type:[Object,String,Number,Boolean]},disabled:{type:Boolean,default:!1},id:{type:String,default:null}},setup(e,{attrs:o,slots:n,expose:a}){var h;let r=(h=e.id)!=null?h:`headlessui-radiogroup-option-${N()}`,t=so("RadioGroupOption"),l=et({name:"RadioGroupLabel"}),i=Me({name:"RadioGroupDescription"}),u=(0,j.ref)(null),p=(0,j.computed)(()=>({value:e.value,disabled:e.disabled})),d=(0,j.ref)(1);a({el:u,$el:u});let f=(0,j.computed)(()=>v(u));(0,j.onMounted)(()=>t.registerOption({id:r,element:f,propsRef:p})),(0,j.onUnmounted)(()=>t.unregisterOption(r));let s=(0,j.computed)(()=>{var x;return((x=t.firstOption.value)==null?void 0:x.id)===r}),S=(0,j.computed)(()=>t.disabled.value||e.disabled),y=(0,j.computed)(()=>t.compare((0,j.toRaw)(t.value.value),(0,j.toRaw)(e.value))),b=(0,j.computed)(()=>S.value?-1:y.value||!t.containsCheckedOption.value&&s.value?0:-1);function g(){var x;t.change(e.value)&&(d.value|=2,(x=v(u))==null||x.focus())}function T(){d.value|=2}function m(){d.value&=-3}return()=>{let{value:x,disabled:c,...O}=e,C={checked:y.value,disabled:S.value,active:Boolean(d.value&2)},R={id:r,ref:u,role:"radio","aria-checked":y.value?"true":"false","aria-labelledby":l.value,"aria-describedby":i.value,"aria-disabled":S.value?!0:void 0,tabIndex:b.value,onClick:S.value?void 0:g,onFocus:S.value?void 0:T,onBlur:S.value?void 0:m};return D({ourProps:R,theirProps:O,slot:C,attrs:o,slots:n,name:"RadioGroupOption"})}}}),Lr=Et,Fr=$e;var Y=require("vue");var fo=Symbol("GroupContext"),kr=(0,Y.defineComponent)({name:"SwitchGroup",props:{as:{type:[Object,String],default:"template"}},setup(e,{slots:o,attrs:n}){let a=(0,Y.ref)(null),r=et({name:"SwitchLabel",props:{htmlFor:(0,Y.computed)(()=>{var i;return(i=a.value)==null?void 0:i.id}),onClick(i){a.value&&(i.currentTarget.tagName==="LABEL"&&i.preventDefault(),a.value.click(),a.value.focus({preventScroll:!0}))}}}),t=Me({name:"SwitchDescription"});return(0,Y.provide)(fo,{switchRef:a,labelledby:r,describedby:t}),()=>D({theirProps:e,ourProps:{},slot:{},slots:o,attrs:n,name:"SwitchGroup"})}}),Hr=(0,Y.defineComponent)({name:"Switch",emits:{"update:modelValue":e=>!0},props:{as:{type:[Object,String],default:"button"},modelValue:{type:Boolean,default:void 0},defaultChecked:{type:Boolean,optional:!0},form:{type:String,optional:!0},name:{type:String,optional:!0},value:{type:String,optional:!0},id:{type:String,default:null},disabled:{type:Boolean,default:!1},tabIndex:{type:Number,default:0}},inheritAttrs:!1,setup(e,{emit:o,attrs:n,slots:a,expose:r}){var T;let t=(T=e.id)!=null?T:`headlessui-switch-${N()}`,l=(0,Y.inject)(fo,null),[i,u]=ye((0,Y.computed)(()=>e.modelValue),m=>o("update:modelValue",m),(0,Y.computed)(()=>e.defaultChecked));function p(){u(!i.value)}let d=(0,Y.ref)(null),f=l===null?d:l.switchRef,s=ae((0,Y.computed)(()=>({as:e.as,type:n.type})),f);r({el:f,$el:f});function S(m){m.preventDefault(),p()}function y(m){m.key===" "?(m.preventDefault(),p()):m.key==="Enter"&&dt(m.currentTarget)}function b(m){m.preventDefault()}let g=(0,Y.computed)(()=>{var m,h;return(h=(m=v(f))==null?void 0:m.closest)==null?void 0:h.call(m,"form")});return(0,Y.onMounted)(()=>{(0,Y.watch)([g],()=>{if(!g.value||e.defaultChecked===void 0)return;function m(){u(e.defaultChecked)}return g.value.addEventListener("reset",m),()=>{var h;(h=g.value)==null||h.removeEventListener("reset",m)}},{immediate:!0})}),()=>{let{name:m,value:h,form:x,tabIndex:c,...O}=e,C={checked:i.value},R={id:t,ref:f,role:"switch",type:s.value,tabIndex:c===-1?0:c,"aria-checked":i.value,"aria-labelledby":l==null?void 0:l.labelledby.value,"aria-describedby":l==null?void 0:l.describedby.value,onClick:S,onKeyup:y,onKeypress:b};return(0,Y.h)(Y.Fragment,[m!=null&&i.value!=null?(0,Y.h)(X,Se({features:4,as:"input",type:"checkbox",hidden:!0,readOnly:!0,checked:i.value,form:x,disabled:O.disabled,name:m,value:h})):null,D({ourProps:R,theirProps:{...n,...le(O,["modelValue","defaultChecked"])},slot:C,attrs:n,slots:a,name:"Switch"})])}}}),Ar=Et,jr=$e;var F=require("vue");var _e=require("vue");var co=(0,_e.defineComponent)({props:{onFocus:{type:Function,required:!0}},setup(e){let o=(0,_e.ref)(!0);return()=>o.value?(0,_e.h)(X,{as:"button",type:"button",features:2,onFocus(n){n.preventDefault();let a,r=50;function t(){var l;if(r--<=0){a&&cancelAnimationFrame(a);return}if((l=e.onFocus)!=null&&l.call(e)){o.value=!1,cancelAnimationFrame(a);return}a=requestAnimationFrame(t)}a=requestAnimationFrame(t)}}):null}});var po=Symbol("TabsContext");function tt(e){let o=(0,F.inject)(po,null);if(o===null){let n=new Error(`<${e} /> is missing a parent <TabGroup /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(n,tt),n}return o}var _t=Symbol("TabsSSRContext"),Nr=(0,F.defineComponent)({name:"TabGroup",emits:{change:e=>!0},props:{as:{type:[Object,String],default:"template"},selectedIndex:{type:[Number],default:null},defaultIndex:{type:[Number],default:0},vertical:{type:[Boolean],default:!1},manual:{type:[Boolean],default:!1}},inheritAttrs:!1,setup(e,{slots:o,attrs:n,emit:a}){var y;let r=(0,F.ref)((y=e.selectedIndex)!=null?y:e.defaultIndex),t=(0,F.ref)([]),l=(0,F.ref)([]),i=(0,F.computed)(()=>e.selectedIndex!==null),u=(0,F.computed)(()=>i.value?e.selectedIndex:r.value);function p(b){var h;let g=re(d.tabs.value,v),T=re(d.panels.value,v),m=g.filter(x=>{var c;return!((c=v(x))!=null&&c.hasAttribute("disabled"))});if(b<0||b>g.length-1){let x=A(r.value===null?0:Math.sign(b-r.value),{[-1]:()=>1,[0]:()=>A(Math.sign(b),{[-1]:()=>0,[0]:()=>0,[1]:()=>1}),[1]:()=>0}),c=A(x,{[0]:()=>g.indexOf(m[0]),[1]:()=>g.indexOf(m[m.length-1])});c!==-1&&(r.value=c),d.tabs.value=g,d.panels.value=T}else{let x=g.slice(0,b),O=[...g.slice(b),...x].find(R=>m.includes(R));if(!O)return;let C=(h=g.indexOf(O))!=null?h:d.selectedIndex.value;C===-1&&(C=d.selectedIndex.value),r.value=C,d.tabs.value=g,d.panels.value=T}}let d={selectedIndex:(0,F.computed)(()=>{var b,g;return(g=(b=r.value)!=null?b:e.defaultIndex)!=null?g:null}),orientation:(0,F.computed)(()=>e.vertical?"vertical":"horizontal"),activation:(0,F.computed)(()=>e.manual?"manual":"auto"),tabs:t,panels:l,setSelectedIndex(b){u.value!==b&&a("change",b),i.value||p(b)},registerTab(b){var T;if(t.value.includes(b))return;let g=t.value[r.value];if(t.value.push(b),t.value=re(t.value,v),!i.value){let m=(T=t.value.indexOf(g))!=null?T:r.value;m!==-1&&(r.value=m)}},unregisterTab(b){let g=t.value.indexOf(b);g!==-1&&t.value.splice(g,1)},registerPanel(b){l.value.includes(b)||(l.value.push(b),l.value=re(l.value,v))},unregisterPanel(b){let g=l.value.indexOf(b);g!==-1&&l.value.splice(g,1)}};(0,F.provide)(po,d);let f=(0,F.ref)({tabs:[],panels:[]}),s=(0,F.ref)(!1);(0,F.onMounted)(()=>{s.value=!0}),(0,F.provide)(_t,(0,F.computed)(()=>s.value?null:f.value));let S=(0,F.computed)(()=>e.selectedIndex);return(0,F.onMounted)(()=>{(0,F.watch)([S],()=>{var b;return p((b=e.selectedIndex)!=null?b:e.defaultIndex)},{immediate:!0})}),(0,F.watchEffect)(()=>{if(!i.value||u.value==null||d.tabs.value.length<=0)return;let b=re(d.tabs.value,v);b.some((T,m)=>v(d.tabs.value[m])!==v(T))&&d.setSelectedIndex(b.findIndex(T=>v(T)===v(d.tabs.value[u.value])))}),()=>{let b={selectedIndex:r.value};return(0,F.h)(F.Fragment,[t.value.length<=0&&(0,F.h)(co,{onFocus:()=>{for(let g of t.value){let T=v(g);if((T==null?void 0:T.tabIndex)===0)return T.focus(),!0}return!1}}),D({theirProps:{...n,...le(e,["selectedIndex","defaultIndex","manual","vertical","onChange"])},ourProps:{},slot:b,slots:o,attrs:n,name:"TabGroup"})])}}}),Br=(0,F.defineComponent)({name:"TabList",props:{as:{type:[Object,String],default:"div"}},setup(e,{attrs:o,slots:n}){let a=tt("TabList");return()=>{let r={selectedIndex:a.selectedIndex.value},t={role:"tablist","aria-orientation":a.orientation.value};return D({ourProps:t,theirProps:e,slot:r,attrs:o,slots:n,name:"TabList"})}}}),Vr=(0,F.defineComponent)({name:"Tab",props:{as:{type:[Object,String],default:"button"},disabled:{type:[Boolean],default:!1},id:{type:String,default:null}},setup(e,{attrs:o,slots:n,expose:a}){var T;let r=(T=e.id)!=null?T:`headlessui-tabs-tab-${N()}`,t=tt("Tab"),l=(0,F.ref)(null);a({el:l,$el:l}),(0,F.onMounted)(()=>t.registerTab(l)),(0,F.onUnmounted)(()=>t.unregisterTab(l));let i=(0,F.inject)(_t),u=(0,F.computed)(()=>{if(i.value){let m=i.value.tabs.indexOf(r);return m===-1?i.value.tabs.push(r)-1:m}return-1}),p=(0,F.computed)(()=>{let m=t.tabs.value.indexOf(l);return m===-1?u.value:m}),d=(0,F.computed)(()=>p.value===t.selectedIndex.value);function f(m){var x;let h=m();if(h===2&&t.activation.value==="auto"){let c=(x=q(l))==null?void 0:x.activeElement,O=t.tabs.value.findIndex(C=>v(C)===c);O!==-1&&t.setSelectedIndex(O)}return h}function s(m){let h=t.tabs.value.map(c=>v(c)).filter(Boolean);if(m.key===" "||m.key==="Enter"){m.preventDefault(),m.stopPropagation(),t.setSelectedIndex(p.value);return}switch(m.key){case"Home":case"PageUp":return m.preventDefault(),m.stopPropagation(),f(()=>Q(h,1));case"End":case"PageDown":return m.preventDefault(),m.stopPropagation(),f(()=>Q(h,8))}if(f(()=>A(t.orientation.value,{vertical(){return m.key==="ArrowUp"?Q(h,18):m.key==="ArrowDown"?Q(h,20):0},horizontal(){return m.key==="ArrowLeft"?Q(h,18):m.key==="ArrowRight"?Q(h,20):0}}))===2)return m.preventDefault()}let S=(0,F.ref)(!1);function y(){var m;S.value||(S.value=!0,!e.disabled&&((m=v(l))==null||m.focus({preventScroll:!0}),t.setSelectedIndex(p.value),Ee(()=>{S.value=!1})))}function b(m){m.preventDefault()}let g=ae((0,F.computed)(()=>({as:e.as,type:o.type})),l);return()=>{var c,O;let m={selected:d.value,disabled:(c=e.disabled)!=null?c:!1},{...h}=e,x={ref:l,onKeydown:s,onMousedown:b,onClick:y,id:r,role:"tab",type:g.value,"aria-controls":(O=v(t.panels.value[p.value]))==null?void 0:O.id,"aria-selected":d.value,tabIndex:d.value?0:-1,disabled:e.disabled?!0:void 0};return D({ourProps:x,theirProps:h,slot:m,attrs:o,slots:n,name:"Tab"})}}}),Kr=(0,F.defineComponent)({name:"TabPanels",props:{as:{type:[Object,String],default:"div"}},setup(e,{slots:o,attrs:n}){let a=tt("TabPanels");return()=>{let r={selectedIndex:a.selectedIndex.value};return D({theirProps:e,ourProps:{},slot:r,attrs:n,slots:o,name:"TabPanels"})}}}),$r=(0,F.defineComponent)({name:"TabPanel",props:{as:{type:[Object,String],default:"div"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},id:{type:String,default:null},tabIndex:{type:Number,default:0}},setup(e,{attrs:o,slots:n,expose:a}){var f;let r=(f=e.id)!=null?f:`headlessui-tabs-panel-${N()}`,t=tt("TabPanel"),l=(0,F.ref)(null);a({el:l,$el:l}),(0,F.onMounted)(()=>t.registerPanel(l)),(0,F.onUnmounted)(()=>t.unregisterPanel(l));let i=(0,F.inject)(_t),u=(0,F.computed)(()=>{if(i.value){let s=i.value.panels.indexOf(r);return s===-1?i.value.panels.push(r)-1:s}return-1}),p=(0,F.computed)(()=>{let s=t.panels.value.indexOf(l);return s===-1?u.value:s}),d=(0,F.computed)(()=>p.value===t.selectedIndex.value);return()=>{var g;let s={selected:d.value},{tabIndex:S,...y}=e,b={ref:l,id:r,role:"tabpanel","aria-labelledby":(g=v(t.tabs.value[p.value]))==null?void 0:g.id,tabIndex:d.value?S:-1};return!d.value&&e.unmount&&!e.static?(0,F.h)(X,{as:"span","aria-hidden":!0,...b}):D({ourProps:b,theirProps:y,slot:s,attrs:o,slots:n,features:3,visible:d.value,name:"TabPanel"})}}});var B=require("vue");function mo(e){let o={called:!1};return(...n)=>{if(!o.called)return o.called=!0,e(...n)}}function Ut(e,...o){e&&o.length>0&&e.classList.add(...o)}function Rt(e,...o){e&&o.length>0&&e.classList.remove(...o)}function _r(e,o){let n=ie();if(!e)return n.dispose;let{transitionDuration:a,transitionDelay:r}=getComputedStyle(e),[t,l]=[a,r].map(i=>{let[u=0]=i.split(",").filter(Boolean).map(p=>p.includes("ms")?parseFloat(p):parseFloat(p)*1e3).sort((p,d)=>d-p);return u});return t!==0?n.setTimeout(()=>o("finished"),t+l):o("finished"),n.add(()=>o("cancelled")),n.dispose}function Wt(e,o,n,a,r,t){let l=ie(),i=t!==void 0?mo(t):()=>{};return Rt(e,...r),Ut(e,...o,...n),l.nextFrame(()=>{Rt(e,...n),Ut(e,...a),l.add(_r(e,u=>(Rt(e,...a,...o),Ut(e,...r),i(u))))}),l.add(()=>Rt(e,...o,...n,...a,...r)),l.add(()=>i("cancelled")),l.dispose}function De(e=""){return e.split(/\s+/).filter(o=>o.length>1)}var zt=Symbol("TransitionContext");function Ur(){return(0,B.inject)(zt,null)!==null}function Wr(){let e=(0,B.inject)(zt,null);if(e===null)throw new Error("A <TransitionChild /> is used but it is missing a parent <TransitionRoot />.");return e}function zr(){let e=(0,B.inject)(Gt,null);if(e===null)throw new Error("A <TransitionChild /> is used but it is missing a parent <TransitionRoot />.");return e}var Gt=Symbol("NestingContext");function Ot(e){return"children"in e?Ot(e.children):e.value.filter(({state:o})=>o==="visible").length>0}function vo(e){let o=(0,B.ref)([]),n=(0,B.ref)(!1);(0,B.onMounted)(()=>n.value=!0),(0,B.onUnmounted)(()=>n.value=!1);function a(t,l=1){let i=o.value.findIndex(({id:u})=>u===t);i!==-1&&(A(l,{[0](){o.value.splice(i,1)},[1](){o.value[i].state="hidden"}}),!Ot(o)&&n.value&&(e==null||e()))}function r(t){let l=o.value.find(({id:i})=>i===t);return l?l.state!=="visible"&&(l.state="visible"):o.value.push({id:t,state:"visible"}),()=>a(t,0)}return{children:o,register:r,unregister:a}}var bo=1,go=(0,B.defineComponent)({props:{as:{type:[Object,String],default:"div"},show:{type:[Boolean],default:null},unmount:{type:[Boolean],default:!0},appear:{type:[Boolean],default:!1},enter:{type:[String],default:""},enterFrom:{type:[String],default:""},enterTo:{type:[String],default:""},entered:{type:[String],default:""},leave:{type:[String],default:""},leaveFrom:{type:[String],default:""},leaveTo:{type:[String],default:""}},emits:{beforeEnter:()=>!0,afterEnter:()=>!0,beforeLeave:()=>!0,afterLeave:()=>!0},setup(e,{emit:o,attrs:n,slots:a,expose:r}){let t=(0,B.ref)(0);function l(){t.value|=8,o("beforeEnter")}function i(){t.value&=-9,o("afterEnter")}function u(){t.value|=4,o("beforeLeave")}function p(){t.value&=-5,o("afterLeave")}if(!Ur()&&xn())return()=>(0,B.h)(ho,{...e,onBeforeEnter:l,onAfterEnter:i,onBeforeLeave:u,onAfterLeave:p},a);let d=(0,B.ref)(null),f=(0,B.computed)(()=>e.unmount?0:1);r({el:d,$el:d});let{show:s,appear:S}=Wr(),{register:y,unregister:b}=zr(),g=(0,B.ref)(s.value?"visible":"hidden"),T={value:!0},m=N(),h={value:!1},x=vo(()=>{!h.value&&g.value!=="hidden"&&(g.value="hidden",b(m),p())});(0,B.onMounted)(()=>{let k=y(m);(0,B.onUnmounted)(k)}),(0,B.watchEffect)(()=>{if(f.value===1&&m){if(s.value&&g.value!=="visible"){g.value="visible";return}A(g.value,{["hidden"]:()=>b(m),["visible"]:()=>y(m)})}});let c=De(e.enter),O=De(e.enterFrom),C=De(e.enterTo),R=De(e.entered),V=De(e.leave),w=De(e.leaveFrom),I=De(e.leaveTo);(0,B.onMounted)(()=>{(0,B.watchEffect)(()=>{if(g.value==="visible"){let k=v(d);if(k instanceof Comment&&k.data==="")throw new Error("Did you forget to passthrough the `ref` to the actual DOM node?")}})});function H(k){let W=T.value&&!S.value,$=v(d);!$||!($ instanceof HTMLElement)||W||(h.value=!0,s.value&&l(),s.value||u(),k(s.value?Wt($,c,O,C,R,J=>{h.value=!1,J==="finished"&&i()}):Wt($,V,w,I,R,J=>{h.value=!1,J==="finished"&&(Ot(x)||(g.value="hidden",b(m),p()))})))}return(0,B.onMounted)(()=>{(0,B.watch)([s],(k,W,$)=>{H($),T.value=!1},{immediate:!0})}),(0,B.provide)(Gt,x),se((0,B.computed)(()=>A(g.value,{["visible"]:1,["hidden"]:2})|t.value)),()=>{let{appear:k,show:W,enter:$,enterFrom:J,enterTo:fe,entered:he,leave:nt,leaveFrom:wt,leaveTo:Ct,...G}=e,Z={ref:d},ue={...G,...S.value&&s.value&&pe.isServer?{class:(0,B.normalizeClass)([n.class,G.class,...c,...O])}:{}};return D({theirProps:ue,ourProps:Z,slot:{},slots:a,attrs:n,features:bo,visible:g.value==="visible",name:"TransitionChild"})}}}),Gr=go,ho=(0,B.defineComponent)({inheritAttrs:!1,props:{as:{type:[Object,String],default:"div"},show:{type:[Boolean],default:null},unmount:{type:[Boolean],default:!0},appear:{type:[Boolean],default:!1},enter:{type:[String],default:""},enterFrom:{type:[String],default:""},enterTo:{type:[String],default:""},entered:{type:[String],default:""},leave:{type:[String],default:""},leaveFrom:{type:[String],default:""},leaveTo:{type:[String],default:""}},emits:{beforeEnter:()=>!0,afterEnter:()=>!0,beforeLeave:()=>!0,afterLeave:()=>!0},setup(e,{emit:o,attrs:n,slots:a}){let r=ne(),t=(0,B.computed)(()=>e.show===null&&r!==null?(r.value&1)===1:e.show);(0,B.watchEffect)(()=>{if(![!0,!1].includes(t.value))throw new Error('A <Transition /> is used but it is missing a `:show="true | false"` prop.')});let l=(0,B.ref)(t.value?"visible":"hidden"),i=vo(()=>{l.value="hidden"}),u=(0,B.ref)(!0),p={show:t,appear:(0,B.computed)(()=>e.appear||!u.value)};return(0,B.onMounted)(()=>{(0,B.watchEffect)(()=>{u.value=!1,t.value?l.value="visible":Ot(i)||(l.value="hidden")})}),(0,B.provide)(Gt,i),(0,B.provide)(zt,p),()=>{let d=le(e,["show","appear","unmount","onBeforeEnter","onBeforeLeave","onAfterEnter","onAfterLeave"]),f={unmount:e.unmount};return D({ourProps:{...f,as:"template"},theirProps:{},slot:{},slots:{...a,default:()=>[(0,B.h)(Gr,{onBeforeEnter:()=>o("beforeEnter"),onAfterEnter:()=>o("afterEnter"),onBeforeLeave:()=>o("beforeLeave"),onAfterLeave:()=>o("afterLeave"),...n,...f,...d},a.default)]},attrs:{},features:bo,visible:l.value==="visible",name:"Transition"})}}});
/*! Bundled license information:

@tanstack/vue-virtual/build/lib/_virtual/_rollupPluginBabelHelpers.mjs:
  (**
   * vue-virtual
   *
   * Copyright (c) TanStack
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE.md file in the root directory of this source tree.
   *
   * @license MIT
   *)

@tanstack/virtual-core/build/lib/_virtual/_rollupPluginBabelHelpers.mjs:
  (**
   * virtual-core
   *
   * Copyright (c) TanStack
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE.md file in the root directory of this source tree.
   *
   * @license MIT
   *)

@tanstack/virtual-core/build/lib/utils.mjs:
  (**
   * virtual-core
   *
   * Copyright (c) TanStack
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE.md file in the root directory of this source tree.
   *
   * @license MIT
   *)

@tanstack/virtual-core/build/lib/index.mjs:
  (**
   * virtual-core
   *
   * Copyright (c) TanStack
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE.md file in the root directory of this source tree.
   *
   * @license MIT
   *)

@tanstack/vue-virtual/build/lib/index.mjs:
  (**
   * vue-virtual
   *
   * Copyright (c) TanStack
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE.md file in the root directory of this source tree.
   *
   * @license MIT
   *)
*/
