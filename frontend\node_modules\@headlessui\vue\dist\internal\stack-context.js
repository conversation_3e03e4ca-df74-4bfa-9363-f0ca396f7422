import{inject as f,onMounted as m,onUnmounted as l,provide as c,watch as p}from"vue";let u=Symbol("StackContext");var s=(e=>(e[e.Add=0]="Add",e[e.Remove=1]="Remove",e))(s||{});function y(){return f(u,()=>{})}function R({type:o,enabled:r,element:e,onUpdate:i}){let a=y();function t(...n){i==null||i(...n),a(...n)}m(()=>{p(r,(n,d)=>{n?t(0,o,e):d===!0&&t(1,o,e)},{immediate:!0,flush:"sync"})}),l(()=>{r.value&&t(1,o,e)}),c(u,t)}export{s as StackMessage,y as useStackContext,R as useStackProvider};
