import{computed as x,defineComponent as y,inject as R,onMounted as v,onUnmounted as D,provide as j,ref as p,unref as C}from"vue";import{useId as h}from'../../hooks/use-id.js';import{render as b}from'../../utils/render.js';let u=Symbol("DescriptionContext");function w(){let t=R(u,null);if(t===null)throw new Error("Missing parent");return t}function k({slot:t=p({}),name:o="Description",props:s={}}={}){let e=p([]);function r(n){return e.value.push(n),()=>{let i=e.value.indexOf(n);i!==-1&&e.value.splice(i,1)}}return j(u,{register:r,slot:t,name:o,props:s}),x(()=>e.value.length>0?e.value.join(" "):void 0)}let K=y({name:"Description",props:{as:{type:[Object,String],default:"p"},id:{type:String,default:null}},setup(t,{attrs:o,slots:s}){var n;let e=(n=t.id)!=null?n:`headlessui-description-${h()}`,r=w();return v(()=>D(r.register(e))),()=>{let{name:i="Description",slot:l=p({}),props:d={}}=r,{...c}=t,f={...Object.entries(d).reduce((a,[g,m])=>Object.assign(a,{[g]:C(m)}),{}),id:e};return b({ourProps:f,theirProps:c,slot:l.value,attrs:o,slots:s,name:i})}}});export{K as Description,k as useDescriptions};
