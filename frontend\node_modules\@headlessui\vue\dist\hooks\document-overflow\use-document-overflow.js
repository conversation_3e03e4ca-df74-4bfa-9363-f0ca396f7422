import{computed as p,watch as s}from"vue";import{useStore as v}from'../../hooks/use-store.js';import{overflows as u}from'./overflow-store.js';function d(t,a,n){let i=v(u),l=p(()=>{let e=t.value?i.value.get(t.value):void 0;return e?e.count>0:!1});return s([t,a],([e,m],[r],o)=>{if(!e||!m)return;u.dispatch("PUSH",e,n);let f=!1;o(()=>{f||(u.dispatch("POP",r!=null?r:e,n),f=!0)})},{immediate:!0}),l}export{d as useDocumentOverflowLockedEffect};
