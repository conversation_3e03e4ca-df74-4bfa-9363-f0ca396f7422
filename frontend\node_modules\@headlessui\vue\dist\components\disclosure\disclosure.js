import{computed as m,defineComponent as b,inject as I,onMounted as P,onUnmounted as h,provide as R,ref as d,watchEffect as w}from"vue";import{useId as E}from'../../hooks/use-id.js';import{useResolveButtonType as H}from'../../hooks/use-resolve-button-type.js';import{State as y,useOpenClosed as L,useOpenClosedProvider as j}from'../../internal/open-closed.js';import{Keys as f}from'../../keyboard.js';import{dom as p}from'../../utils/dom.js';import{match as x}from'../../utils/match.js';import{Features as B,render as g}from'../../utils/render.js';var $=(o=>(o[o.Open=0]="Open",o[o.Closed=1]="Closed",o))($||{});let T=Symbol("DisclosureContext");function O(t){let r=I(T,null);if(r===null){let o=new Error(`<${t} /> is missing a parent <Disclosure /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(o,O),o}return r}let k=Symbol("DisclosurePanelContext");function U(){return I(k,null)}let N=b({name:"Disclosure",props:{as:{type:[Object,String],default:"template"},defaultOpen:{type:[Boolean],default:!1}},setup(t,{slots:r,attrs:o}){let s=d(t.defaultOpen?0:1),e=d(null),i=d(null),n={buttonId:d(`headlessui-disclosure-button-${E()}`),panelId:d(`headlessui-disclosure-panel-${E()}`),disclosureState:s,panel:e,button:i,toggleDisclosure(){s.value=x(s.value,{[0]:1,[1]:0})},closeDisclosure(){s.value!==1&&(s.value=1)},close(l){n.closeDisclosure();let a=(()=>l?l instanceof HTMLElement?l:l.value instanceof HTMLElement?p(l):p(n.button):p(n.button))();a==null||a.focus()}};return R(T,n),j(m(()=>x(s.value,{[0]:y.Open,[1]:y.Closed}))),()=>{let{defaultOpen:l,...a}=t,c={open:s.value===0,close:n.close};return g({theirProps:a,ourProps:{},slot:c,slots:r,attrs:o,name:"Disclosure"})}}}),Q=b({name:"DisclosureButton",props:{as:{type:[Object,String],default:"button"},disabled:{type:[Boolean],default:!1},id:{type:String,default:null}},setup(t,{attrs:r,slots:o,expose:s}){let e=O("DisclosureButton"),i=U(),n=m(()=>i===null?!1:i.value===e.panelId.value);P(()=>{n.value||t.id!==null&&(e.buttonId.value=t.id)}),h(()=>{n.value||(e.buttonId.value=null)});let l=d(null);s({el:l,$el:l}),n.value||w(()=>{e.button.value=l.value});let a=H(m(()=>({as:t.as,type:r.type})),l);function c(){var u;t.disabled||(n.value?(e.toggleDisclosure(),(u=p(e.button))==null||u.focus()):e.toggleDisclosure())}function D(u){var S;if(!t.disabled)if(n.value)switch(u.key){case f.Space:case f.Enter:u.preventDefault(),u.stopPropagation(),e.toggleDisclosure(),(S=p(e.button))==null||S.focus();break}else switch(u.key){case f.Space:case f.Enter:u.preventDefault(),u.stopPropagation(),e.toggleDisclosure();break}}function v(u){switch(u.key){case f.Space:u.preventDefault();break}}return()=>{var C;let u={open:e.disclosureState.value===0},{id:S,...K}=t,M=n.value?{ref:l,type:a.value,onClick:c,onKeydown:D}:{id:(C=e.buttonId.value)!=null?C:S,ref:l,type:a.value,"aria-expanded":e.disclosureState.value===0,"aria-controls":e.disclosureState.value===0||p(e.panel)?e.panelId.value:void 0,disabled:t.disabled?!0:void 0,onClick:c,onKeydown:D,onKeyup:v};return g({ourProps:M,theirProps:K,slot:u,attrs:r,slots:o,name:"DisclosureButton"})}}}),V=b({name:"DisclosurePanel",props:{as:{type:[Object,String],default:"div"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},id:{type:String,default:null}},setup(t,{attrs:r,slots:o,expose:s}){let e=O("DisclosurePanel");P(()=>{t.id!==null&&(e.panelId.value=t.id)}),h(()=>{e.panelId.value=null}),s({el:e.panel,$el:e.panel}),R(k,e.panelId);let i=L(),n=m(()=>i!==null?(i.value&y.Open)===y.Open:e.disclosureState.value===0);return()=>{var v;let l={open:e.disclosureState.value===0,close:e.close},{id:a,...c}=t,D={id:(v=e.panelId.value)!=null?v:a,ref:e.panel};return g({ourProps:D,theirProps:c,slot:l,attrs:r,slots:o,features:B.RenderStrategy|B.Static,visible:n.value,name:"DisclosurePanel"})}}});export{N as Disclosure,Q as DisclosureButton,V as DisclosurePanel};
