import{h as m,ref as s}from"vue";import{Features as d,Hidden as c}from'../internal/hidden.js';import{dom as T}from'../utils/dom.js';import{getOwnerDocument as E}from'../utils/owner.js';function N({defaultContainers:o=[],portals:i,mainTreeNodeRef:H}={}){let t=s(null),r=E(t);function u(){var l,f,a;let n=[];for(let e of o)e!==null&&(e instanceof HTMLElement?n.push(e):"value"in e&&e.value instanceof HTMLElement&&n.push(e.value));if(i!=null&&i.value)for(let e of i.value)n.push(e);for(let e of(l=r==null?void 0:r.querySelectorAll("html > *, body > *"))!=null?l:[])e!==document.body&&e!==document.head&&e instanceof HTMLElement&&e.id!=="headlessui-portal-root"&&(e.contains(T(t))||e.contains((a=(f=T(t))==null?void 0:f.getRootNode())==null?void 0:a.host)||n.some(M=>e.contains(M))||n.push(e));return n}return{resolveContainers:u,contains(n){return u().some(l=>l.contains(n))},mainTreeNodeRef:t,MainTreeNode(){return H!=null?null:m(c,{features:d.Hidden,ref:t})}}}function v(){let o=s(null);return{mainTreeNodeRef:o,MainTreeNode(){return m(c,{features:d.Hidden,ref:o})}}}export{v as useMainTreeNode,N as useRootContainers};
