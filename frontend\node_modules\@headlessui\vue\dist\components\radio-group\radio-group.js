import{computed as o,defineComponent as F,Fragment as _,h as C,inject as $,onMounted as D,onUnmounted as U,provide as W,ref as k,toRaw as y,watch as J}from"vue";import{useControllable as q}from'../../hooks/use-controllable.js';import{useId as x}from'../../hooks/use-id.js';import{useTreeWalker as Q}from'../../hooks/use-tree-walker.js';import{Features as X,Hidden as Y}from'../../internal/hidden.js';import{Keys as h}from'../../keyboard.js';import{dom as E}from'../../utils/dom.js';import{Focus as w,focusIn as I,FocusResult as P,sortByDomNode as Z}from'../../utils/focus-management.js';import{attemptSubmit as z,objectToFormEntries as ee}from'../../utils/form.js';import{getOwnerDocument as A}from'../../utils/owner.js';import{compact as te,omit as ae,render as B}from'../../utils/render.js';import{Description as ne,useDescriptions as V}from'../description/description.js';import{Label as re,useLabels as j}from'../label/label.js';function le(t,m){return t===m}let H=Symbol("RadioGroupContext");function N(t){let m=$(H,null);if(m===null){let u=new Error(`<${t} /> is missing a parent <RadioGroup /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(u,N),u}return m}let he=F({name:"RadioGroup",emits:{"update:modelValue":t=>!0},props:{as:{type:[Object,String],default:"div"},disabled:{type:[Boolean],default:!1},by:{type:[String,Function],default:()=>le},modelValue:{type:[Object,String,Number,Boolean],default:void 0},defaultValue:{type:[Object,String,Number,Boolean],default:void 0},form:{type:String,optional:!0},name:{type:String,optional:!0},id:{type:String,default:null}},inheritAttrs:!1,setup(t,{emit:m,attrs:u,slots:S,expose:g}){var O;let d=(O=t.id)!=null?O:`headlessui-radiogroup-${x()}`,p=k(null),l=k([]),R=j({name:"RadioGroupLabel"}),T=V({name:"RadioGroupDescription"});g({el:p,$el:p});let[f,G]=q(o(()=>t.modelValue),e=>m("update:modelValue",e),o(()=>t.defaultValue)),s={options:l,value:f,disabled:o(()=>t.disabled),firstOption:o(()=>l.value.find(e=>!e.propsRef.disabled)),containsCheckedOption:o(()=>l.value.some(e=>s.compare(y(e.propsRef.value),y(t.modelValue)))),compare(e,a){if(typeof t.by=="string"){let n=t.by;return(e==null?void 0:e[n])===(a==null?void 0:a[n])}return t.by(e,a)},change(e){var n;if(t.disabled||s.compare(y(f.value),y(e)))return!1;let a=(n=l.value.find(i=>s.compare(y(i.propsRef.value),y(e))))==null?void 0:n.propsRef;return a!=null&&a.disabled?!1:(G(e),!0)},registerOption(e){l.value.push(e),l.value=Z(l.value,a=>a.element)},unregisterOption(e){let a=l.value.findIndex(n=>n.id===e);a!==-1&&l.value.splice(a,1)}};W(H,s),Q({container:o(()=>E(p)),accept(e){return e.getAttribute("role")==="radio"?NodeFilter.FILTER_REJECT:e.hasAttribute("role")?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT},walk(e){e.setAttribute("role","none")}});function v(e){if(!p.value||!p.value.contains(e.target))return;let a=l.value.filter(n=>n.propsRef.disabled===!1).map(n=>n.element);switch(e.key){case h.Enter:z(e.currentTarget);break;case h.ArrowLeft:case h.ArrowUp:if(e.preventDefault(),e.stopPropagation(),I(a,w.Previous|w.WrapAround)===P.Success){let i=l.value.find(r=>{var c;return r.element===((c=A(p))==null?void 0:c.activeElement)});i&&s.change(i.propsRef.value)}break;case h.ArrowRight:case h.ArrowDown:if(e.preventDefault(),e.stopPropagation(),I(a,w.Next|w.WrapAround)===P.Success){let i=l.value.find(r=>{var c;return r.element===((c=A(r.element))==null?void 0:c.activeElement)});i&&s.change(i.propsRef.value)}break;case h.Space:{e.preventDefault(),e.stopPropagation();let n=l.value.find(i=>{var r;return i.element===((r=A(i.element))==null?void 0:r.activeElement)});n&&s.change(n.propsRef.value)}break}}let b=o(()=>{var e;return(e=E(p))==null?void 0:e.closest("form")});return D(()=>{J([b],()=>{if(!b.value||t.defaultValue===void 0)return;function e(){s.change(t.defaultValue)}return b.value.addEventListener("reset",e),()=>{var a;(a=b.value)==null||a.removeEventListener("reset",e)}},{immediate:!0})}),()=>{let{disabled:e,name:a,form:n,...i}=t,r={ref:p,id:d,role:"radiogroup","aria-labelledby":R.value,"aria-describedby":T.value,onKeydown:v};return C(_,[...a!=null&&f.value!=null?ee({[a]:f.value}).map(([c,L])=>C(Y,te({features:X.Hidden,key:c,as:"input",type:"hidden",hidden:!0,readOnly:!0,form:n,disabled:e,name:c,value:L}))):[],B({ourProps:r,theirProps:{...u,...ae(i,["modelValue","defaultValue","by"])},slot:{},attrs:u,slots:S,name:"RadioGroup"})])}}});var ie=(u=>(u[u.Empty=1]="Empty",u[u.Active=2]="Active",u))(ie||{});let Oe=F({name:"RadioGroupOption",props:{as:{type:[Object,String],default:"div"},value:{type:[Object,String,Number,Boolean]},disabled:{type:Boolean,default:!1},id:{type:String,default:null}},setup(t,{attrs:m,slots:u,expose:S}){var i;let g=(i=t.id)!=null?i:`headlessui-radiogroup-option-${x()}`,d=N("RadioGroupOption"),p=j({name:"RadioGroupLabel"}),l=V({name:"RadioGroupDescription"}),R=k(null),T=o(()=>({value:t.value,disabled:t.disabled})),f=k(1);S({el:R,$el:R});let G=o(()=>E(R));D(()=>d.registerOption({id:g,element:G,propsRef:T})),U(()=>d.unregisterOption(g));let s=o(()=>{var r;return((r=d.firstOption.value)==null?void 0:r.id)===g}),v=o(()=>d.disabled.value||t.disabled),b=o(()=>d.compare(y(d.value.value),y(t.value))),O=o(()=>v.value?-1:b.value||!d.containsCheckedOption.value&&s.value?0:-1);function e(){var r;d.change(t.value)&&(f.value|=2,(r=E(R))==null||r.focus())}function a(){f.value|=2}function n(){f.value&=-3}return()=>{let{value:r,disabled:c,...L}=t,K={checked:b.value,disabled:v.value,active:Boolean(f.value&2)},M={id:g,ref:R,role:"radio","aria-checked":b.value?"true":"false","aria-labelledby":p.value,"aria-describedby":l.value,"aria-disabled":v.value?!0:void 0,tabIndex:O.value,onClick:v.value?void 0:e,onFocus:v.value?void 0:a,onBlur:v.value?void 0:n};return B({ourProps:M,theirProps:L,slot:K,attrs:m,slots:u,name:"RadioGroupOption"})}}}),ke=re,Ee=ne;export{he as RadioGroup,Ee as RadioGroupDescription,ke as RadioGroupLabel,Oe as RadioGroupOption};
