{"name": "@headlessui/vue", "version": "1.7.23", "description": "A set of completely unstyled, fully accessible UI components for Vue 3, designed to integrate beautifully with Tailwind CSS.", "main": "dist/index.cjs", "typings": "dist/index.d.ts", "module": "dist/headlessui.esm.js", "license": "MIT", "files": ["README.md", "dist"], "exports": {"types": {"import": "./dist/index.d.ts", "require": "./dist/index.d.cts"}, "import": "./dist/headlessui.esm.js", "require": "./dist/index.cjs"}, "type": "module", "sideEffects": false, "engines": {"node": ">=10"}, "repository": {"type": "git", "url": "git+https://github.com/tailwindlabs/headlessui.git", "directory": "packages/@headlessui-vue"}, "publishConfig": {"access": "public"}, "scripts": {"prepublishOnly": "npm run build", "build": "../../scripts/build.sh --external:vue", "watch": "../../scripts/watch.sh --external:vue", "test": "../../scripts/test.sh", "lint": "../../scripts/lint.sh", "lint-types": "yarn run attw -P", "playground": "yarn workspace playground-vue dev", "clean": "rimraf ./dist"}, "peerDependencies": {"vue": "^3.2.0"}, "devDependencies": {"@testing-library/vue": "^5.8.2", "@vue/test-utils": "^2.0.0-rc.18", "vue": "^3.2.29"}, "dependencies": {"@tanstack/vue-virtual": "^3.0.0-beta.60"}}