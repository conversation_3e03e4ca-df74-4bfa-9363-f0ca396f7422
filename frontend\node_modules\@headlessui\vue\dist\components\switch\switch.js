import{computed as u,defineComponent as v,Fragment as H,h as S,inject as M,onMounted as I,provide as P,ref as w,watch as j}from"vue";import{useControllable as G}from'../../hooks/use-controllable.js';import{useId as V}from'../../hooks/use-id.js';import{useResolveButtonType as F}from'../../hooks/use-resolve-button-type.js';import{Features as O,Hidden as A}from'../../internal/hidden.js';import{Keys as g}from'../../keyboard.js';import{dom as N}from'../../utils/dom.js';import{attemptSubmit as $}from'../../utils/form.js';import{compact as U,omit as _,render as k}from'../../utils/render.js';import{Description as q,useDescriptions as z}from'../description/description.js';import{Label as J,useLabels as Q}from'../label/label.js';let C=Symbol("GroupContext"),oe=v({name:"SwitchGroup",props:{as:{type:[Object,String],default:"template"}},setup(l,{slots:c,attrs:i}){let r=w(null),f=Q({name:"SwitchLabel",props:{htmlFor:u(()=>{var t;return(t=r.value)==null?void 0:t.id}),onClick(t){r.value&&(t.currentTarget.tagName==="LABEL"&&t.preventDefault(),r.value.click(),r.value.focus({preventScroll:!0}))}}}),p=z({name:"SwitchDescription"});return P(C,{switchRef:r,labelledby:f,describedby:p}),()=>k({theirProps:l,ourProps:{},slot:{},slots:c,attrs:i,name:"SwitchGroup"})}}),ue=v({name:"Switch",emits:{"update:modelValue":l=>!0},props:{as:{type:[Object,String],default:"button"},modelValue:{type:Boolean,default:void 0},defaultChecked:{type:Boolean,optional:!0},form:{type:String,optional:!0},name:{type:String,optional:!0},value:{type:String,optional:!0},id:{type:String,default:null},disabled:{type:Boolean,default:!1},tabIndex:{type:Number,default:0}},inheritAttrs:!1,setup(l,{emit:c,attrs:i,slots:r,expose:f}){var h;let p=(h=l.id)!=null?h:`headlessui-switch-${V()}`,n=M(C,null),[t,s]=G(u(()=>l.modelValue),e=>c("update:modelValue",e),u(()=>l.defaultChecked));function m(){s(!t.value)}let E=w(null),o=n===null?E:n.switchRef,L=F(u(()=>({as:l.as,type:i.type})),o);f({el:o,$el:o});function D(e){e.preventDefault(),m()}function R(e){e.key===g.Space?(e.preventDefault(),m()):e.key===g.Enter&&$(e.currentTarget)}function x(e){e.preventDefault()}let d=u(()=>{var e,a;return(a=(e=N(o))==null?void 0:e.closest)==null?void 0:a.call(e,"form")});return I(()=>{j([d],()=>{if(!d.value||l.defaultChecked===void 0)return;function e(){s(l.defaultChecked)}return d.value.addEventListener("reset",e),()=>{var a;(a=d.value)==null||a.removeEventListener("reset",e)}},{immediate:!0})}),()=>{let{name:e,value:a,form:K,tabIndex:y,...b}=l,T={checked:t.value},B={id:p,ref:o,role:"switch",type:L.value,tabIndex:y===-1?0:y,"aria-checked":t.value,"aria-labelledby":n==null?void 0:n.labelledby.value,"aria-describedby":n==null?void 0:n.describedby.value,onClick:D,onKeyup:R,onKeypress:x};return S(H,[e!=null&&t.value!=null?S(A,U({features:O.Hidden,as:"input",type:"checkbox",hidden:!0,readOnly:!0,checked:t.value,form:K,disabled:b.disabled,name:e,value:a})):null,k({ourProps:B,theirProps:{...i,..._(b,["modelValue","defaultChecked"])},slot:T,attrs:i,slots:r,name:"Switch"})])}}}),de=J,ce=q;export{ue as Switch,ce as SwitchDescription,oe as SwitchGroup,de as SwitchLabel};
