import{computed as v,defineComponent as x,inject as L,onMounted as k,onUnmounted as C,provide as j,ref as y,unref as h}from"vue";import{useId as w}from'../../hooks/use-id.js';import{render as R}from'../../utils/render.js';let a=Symbol("LabelContext");function d(){let t=L(a,null);if(t===null){let n=new Error("You used a <Label /> component, but it is not inside a parent.");throw Error.captureStackTrace&&Error.captureStackTrace(n,d),n}return t}function E({slot:t={},name:n="Label",props:i={}}={}){let e=y([]);function o(r){return e.value.push(r),()=>{let l=e.value.indexOf(r);l!==-1&&e.value.splice(l,1)}}return j(a,{register:o,slot:t,name:n,props:i}),v(()=>e.value.length>0?e.value.join(" "):void 0)}let K=x({name:"Label",props:{as:{type:[Object,String],default:"label"},passive:{type:[Boolean],default:!1},id:{type:String,default:null}},setup(t,{slots:n,attrs:i}){var r;let e=(r=t.id)!=null?r:`headlessui-label-${w()}`,o=d();return k(()=>C(o.register(e))),()=>{let{name:l="Label",slot:p={},props:c={}}=o,{passive:f,...s}=t,u={...Object.entries(c).reduce((b,[g,m])=>Object.assign(b,{[g]:h(m)}),{}),id:e};return f&&(delete u.onClick,delete u.htmlFor,delete s.onClick),R({ourProps:u,theirProps:s,slot:p,attrs:i,slots:n,name:l})}}});export{K as Label,E as useLabels};
